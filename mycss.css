* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Microsoft YaHei", sans-serif;
}

.header-title {
    background-color: #f8f9fa;
    color: #00706B;
    /* text-indent: 50px; */
    bottom: 10px;
    padding: 15px 20px;
    font-size: 24px;
    font-weight: bold;
    letter-spacing: 1px;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 0;
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-title .header-left {
    display: flex;
    align-items: center;
}

.header-title .header-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-title .user-info {
    font-size: 14px;
    color: #666;
    font-weight: normal;
    padding: 4px 8px;
    background-color: rgba(0, 112, 107, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(0, 112, 107, 0.2);
}

.logout-btn {
    background: none;
    border: 1px solid #dc3545;
    color: #dc3545;
    padding: 3px 8px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logout-btn:hover {
    background-color: #dc3545;
    color: white;
    transform: translateY(-1px);
}

.stats-btn {
    background: none;
    border: 1px solid #007bff;
    color: #007bff;
    padding: 3px 8px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
}

.stats-btn:hover {
    background-color: #007bff;
    color: white;
    transform: translateY(-1px);
}

body {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    background-color: #f8f9fa;
}

.main-container {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.screen {
    height: 100%;
    overflow-y: auto;
    padding: 15px;
    border-right: 1px solid #ddd;
    background-color: white;
}

#nav-screen {
    width: 280px;
    background-color: #f5f5f5;
}

/* 移除不再需要的nav-header样式 */

#project-screen {
    width: 350px;
}

#review-screen {
    flex: 1;
}

h2 {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    color: #333;
}

h3 {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
    color: #2c3e50;
    font-size: 17px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.city-item {
    margin-bottom: 15px;
}

.city-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.city-name {
    padding: 8px 10px;
    background-color: #00706B;
    color: white;
    cursor: pointer;
    border-radius: 4px;
    font-weight: bold;
    flex: 1;
}

.city-stats {
    display: flex;
    margin-left: 10px;
}

.stat-badge {
    width: 30px;
    height: 20px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    margin-left: 5px;
    color: white;
}

.total-count {
    background-color: #7f8c8d;
}

.completed-count {
    background-color: #2ecc71;
}

.option-list {
    margin-top: 5px;
    margin-left: 15px;
    display: none;
}

.option-item {
    padding: 12px 16px;
    margin-bottom: 6px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    transition: all 0.2s ease;
}

.option-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background-color: #00706b;
    border-radius: 0 4px 4px 0;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.option-item:hover {
    border-color: #00706b;
}

.option-item.active {
    background-color: #f8f9fa;
    border-color: #00706b;
}

.option-item.active::before {
    opacity: 1;
}





.option-name {
    font-weight: 500;
    font-size: 14px;
    line-height: 1.2;
    margin-bottom: 2px;
}

.type-stats {
    display: flex;
    margin-top: 6px;
    margin-left: 0;
    flex-wrap: wrap;
    gap: 3px;
}

.count-badge {
    width: 24px;
    height: 18px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
    color: white;
    transition: all 0.2s ease;
}

.red-count {
    background-color: #e74c3c;
}

.yellow-count {
    background-color: #f1c40f;
}

.blue-count {
    background-color: #3498db;
}

.total-type-count {
    background-color: #7f8c8d;
}

.completed-type-count {
    background-color: #2ecc71;
}

/* 添加徽章点击样式 */
.count-badge {
    cursor: pointer;
}

.count-badge:hover {
    opacity: 0.8;
}

.count-badge:active {
    transform: scale(0.95);
}

/* 项目卡片优化样式 */
.project-item {
    padding: 16px 20px;
    margin-bottom: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border-radius: 12px;
    border: 1px solid #e8eaed;
    cursor: pointer;
    position: relative;
    padding-bottom: 16px;
    padding-left: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

/* 项目内容区域 */
.project-content {
    flex: 1;
    min-width: 0; /* 防止内容溢出 */
}

/* 项目操作区域 */
.project-actions {
    position: absolute;
    bottom: 12px;
    right: 12px;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 10;
}

/* 悬停时显示删除按钮 */
.project-item:hover .project-actions {
    opacity: 1;
    transform: translateY(0);
}

/* 删除按钮样式 */
.delete-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: #e74c3c;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.delete-btn:hover {
    background: #c0392b;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
}

.delete-btn:active {
    transform: scale(0.95);
}



/* 左边状态指示条 */
.project-item {
    --left-bar-color: #e0e0e0;
    --left-bar-width: 5px;
}

.project-item::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: var(--left-bar-width);
    background: linear-gradient(180deg, var(--left-bar-color) 0%, var(--left-bar-color) 100%);
    border-top-left-radius: 12px;
    border-bottom-left-radius: 12px;
    transition: width 0.3s ease;
}

/* 悬停效果优化 */
.project-item:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-color: #00706B;
    box-shadow: 0 4px 16px rgba(0, 112, 107, 0.12);
    transform: translateY(-2px);
}

.project-item:hover::before {
    width: 8px;
}

/* 激活状态优化 */
.project-item.active {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border-color: #00706B;
    box-shadow: 0 6px 20px rgba(0, 112, 107, 0.15);
    transform: translateY(-1px);
}

.project-item.active::before {
    width: 8px;
    background: linear-gradient(180deg, #00706B 0%, #4a9b96 100%);
}

/* 项目卡片加载动画 */
@keyframes projectCardFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.project-item {
    animation: projectCardFadeIn 0.4s ease-out;
}

/* 为不同的卡片添加延迟动画 */
.project-item:nth-child(1) { animation-delay: 0.1s; }
.project-item:nth-child(2) { animation-delay: 0.15s; }
.project-item:nth-child(3) { animation-delay: 0.2s; }
.project-item:nth-child(4) { animation-delay: 0.25s; }
.project-item:nth-child(5) { animation-delay: 0.3s; }
.project-item:nth-child(6) { animation-delay: 0.35s; }
.project-item:nth-child(7) { animation-delay: 0.4s; }
.project-item:nth-child(8) { animation-delay: 0.45s; }

/* 点击反馈动画 */
.project-item:active {
    transform: translateY(-1px) scale(0.98);
    transition: transform 0.1s ease;
}

/* 状态标签悬停效果 */
.project-status:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 项目状态标签优化 */
.project-status {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    position: absolute;
    right: 12px;
    top: 12px;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    min-width: 60px;
    transition: all 0.3s ease;
}

/* 状态颜色优化 */
.status-pending-submit {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(238, 90, 82, 0.3);
}

.status-rejected {
    background: linear-gradient(135deg, #ffd93d 0%, #ff9f43 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(255, 159, 67, 0.3);
}

.status-pending {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
}

/* 项目名称优化 */
.project-name {
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
    font-size: 15px;
    line-height: 1.4;
    padding-right: 80px; /* 为状态标签留出空间 */
}

/* 项目元信息优化 */
.project-meta {
    display: block;
    margin-bottom: 6px;
    font-size: 13px;
    color: #6c757d;
    line-height: 1.5;
}

.project-meta:last-of-type {
    margin-bottom: 0;
}

.project-meta span {
    display: inline-block;
    background-color: rgba(108, 117, 125, 0.08);
    padding: 2px 8px;
    border-radius: 6px;
    margin-right: 6px;
    margin-bottom: 4px;
    font-size: 12px;
    color: #495057;
    border: 1px solid rgba(108, 117, 125, 0.1);
}

.review-item {
    padding: 10px;
    margin-bottom: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #ddd;
    position: relative;
}

.review-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.file-status {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.approved {
    background-color: #2ecc71;
    color: white;
}

.rejected {
    background-color: #f1c40f;
    color: black;
}

.pending {
    background-color: #3498db;
    color: white;
}

.pending-submit {
    background-color: #e74c3c;
    color: white;
}

.reason-box {
    margin-top: 10px;
    padding: 8px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #555;
    font-size: 14px;
}

.upload-btn {
    margin-top: 10px;
    padding: 5px 10px;
    background-color: #27ae60;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.upload-gp-btn {
    margin-top: 10px;
    padding: 5px 10px;
    background-color: #27ae60;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.download-btn {
    margin-top: 10px;
    padding: 5px 10px;
    background-color: #2980b9;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    margin-right: 5px;
}

.pending-btn {
    margin-top: 10px;
    padding: 5px 10px;
    background-color: #9b59b6;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    margin-right: 5px;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

/* 图标样式 */
.icon-check::before {
    content: "✓";
    font-weight: bold;
    font-size: 14px;
}

.icon-refresh::before {
    content: "↻";
    font-weight: bold;
    font-size: 14px;
    display: inline-block;
}

/* 重新审核按钮特殊样式 */
.pending-btn-reaudit {
    background-color: #e67e22 !important;
}

.pending-btn-reaudit:hover {
    background-color: #d35400 !important;
}

/* 普通审核按钮悬停效果 */
.pending-btn:hover {
    background-color: #8e44ad;
}

.hidden {
    display: none;
}

/* 无选择状态优化 */
.no-selection {
    color: #9ca3af;
    font-style: italic;
    text-align: center;
    margin: 40px 20px;
    padding: 30px;
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    border-radius: 16px;
    border: 2px dashed #d1d5db;
    font-size: 15px;
    line-height: 1.6;
    position: relative;
}

.no-selection::before {
    content: "📋";
    display: block;
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.6;
}

/* 搜索容器优化 */
.search-container {
    margin-bottom: 20px;
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 搜索输入框优化 */
.search-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e8eaed;
    border-radius: 25px;
    font-size: 14px;
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.search-input:focus {
    outline: none;
    border-color: #00706B;
    box-shadow: 0 0 0 3px rgba(0, 112, 107, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08);
    background: #ffffff;
    transform: translateY(-1px);
}

.search-input::placeholder {
    color: #9ca3af;
    font-style: italic;
}

/* 搜索清除按钮优化 */
.search-clear {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    color: #6c757d;
    cursor: pointer;
    font-size: 14px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-clear::before {
    content: "×";
    font-size: 16px;
    line-height: 1;
    font-weight: 600;
}

.search-clear:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    color: #495057;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* 分页控件优化 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    padding: 16px 0;
    border-top: 2px solid #f1f3f4;
    flex-wrap: nowrap;
    gap: 6px;
    font-size: 14px;
    max-width: 100%;
    overflow-x: auto;
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 分页按钮优化 */
.page-btn {
    padding: 8px 12px;
    min-width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e8eaed;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.page-btn:hover:not(.disabled) {
    background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
    border-color: #00706B;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0, 112, 107, 0.15);
}

.page-btn.active {
    background: linear-gradient(135deg, #00706B 0%, #4a9b96 100%);
    color: white;
    border-color: #00706B;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(0, 112, 107, 0.25);
}

.page-btn.disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: #f8f9fa;
    border-color: #e9ecef;
}

.page-nav-btn {
    font-size: 16px;
    font-weight: bold;
}

.page-ellipsis {
    margin: 0 2px;
    color: #888;
    user-select: none;
    font-size: 13px; /* 增大字体大小 */
}

.page-jump {
    display: flex;
    align-items: center;
    margin-left: 5px;
    font-size: 13px; /* 增大字体大小 */
}

.page-jump input {
    width: 35px; /* 增大输入框宽度 */
    height: 28px; /* 增大输入框高度 */
    margin: 0 3px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px; /* 增大输入框字体大小 */
}

.page-jump button {
    padding: 4px 8px; /* 增大按钮内边距 */
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: pointer;
    font-size: 13px; /* 增大按钮字体大小 */
}

.page-jump button:hover {
    background-color: #e0e0e0;
}

/* 用户详情侧边栏样式 - 默认折叠状态 */
#userinfo-screen {
    width: 0px; /* 折叠状态完全隐藏，节省最大空间 */
    min-width: 0px;
    overflow: hidden; /* 折叠状态下隐藏所有内容 */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border-left: none; /* 折叠状态下移除边框 */
    background: transparent; /* 折叠状态下移除背景 */
    padding: 0; /* 移除所有内边距 */
    margin: 0; /* 移除所有外边距 */
}

/* 展开状态 */
#userinfo-screen.expanded {
    width: 360px;
    min-width: 360px;
    overflow: visible; /* 展开状态显示所有内容 */
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%); /* 展开时恢复背景 */
    border-left: 1px solid #e0e0e0; /* 展开时恢复边框 */
    padding: 0; /* 保持无内边距 */
    margin: 0; /* 保持无外边距 */
}

/* 折叠按钮 - 位于顶部标题栏 */
.userinfo-toggle-btn.header-toggle {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #00706B, #4a9b96);
    border: 2px solid #ffffff;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 13px;
    z-index: 1500;
    box-shadow: 0 2px 8px rgba(0, 112, 107, 0.4);
    transition: all 0.3s ease;
    margin: 0 8px; /* 与其他按钮保持一致的间距 */
    flex-shrink: 0;
}

.userinfo-toggle-btn.header-toggle:hover {
    background: linear-gradient(135deg, #005a56, #3d8a85);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 112, 107, 0.6);
}

.userinfo-toggle-btn.header-toggle:active {
    transform: scale(0.95);
}

/* 默认折叠状态下的内容完全隐藏 */
#userinfo-screen .userinfo-content {
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    overflow: hidden;
    padding: 0; /* 折叠状态下移除内边距 */
    margin: 0; /* 折叠状态下移除外边距 */
    width: 0; /* 折叠状态下宽度为0 */
    height: 0; /* 折叠状态下高度为0 */
}

#userinfo-screen .userinfo-header {
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    padding: 0; /* 折叠状态下移除内边距 */
    margin: 0; /* 折叠状态下移除外边距 */
    background: transparent; /* 折叠状态下移除背景 */
    border-bottom: none; /* 折叠状态下移除边框 */
    width: 0; /* 折叠状态下宽度为0 */
    height: 0; /* 折叠状态下高度为0 */
}

/* 展开状态下的内容显示 */
#userinfo-screen.expanded .userinfo-content {
    opacity: 1;
    pointer-events: auto;
    overflow-y: auto; /* 展开状态下启用垂直滚动 */
    overflow-x: hidden; /* 隐藏水平滚动 */
    max-height: calc(100vh - 120px); /* 限制最大高度，确保滚动条出现 */
    padding: 0 15px 15px 15px; /* 展开状态下恢复内边距 */
    width: auto; /* 展开状态下恢复宽度 */
    height: auto; /* 展开状态下恢复高度 */
}

#userinfo-screen.expanded .userinfo-header {
    opacity: 1;
    pointer-events: auto;
    padding: 15px; /* 展开状态下恢复内边距 */
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); /* 展开状态下恢复背景 */
    /*border-bottom: 2px solid #e3f2fd; /* 展开状态下恢复边框 */
    position: sticky; /* 保持头部固定 */
    top: 0;
    z-index: 10;
    flex-shrink: 0; /* 防止头部被压缩 */
    width: auto; /* 展开状态下恢复宽度 */
    height: auto; /* 展开状态下恢复高度 */
}

/* 全局隐藏滚动条但保留滚动功能 */
* {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
}

*::-webkit-scrollbar {
    display: none; /* WebKit */
}

/* 确保所有可滚动区域都隐藏滚动条 */
.screen::-webkit-scrollbar,
#userinfo-screen::-webkit-scrollbar,
#userinfo-screen .userinfo-content::-webkit-scrollbar,
#userinfo-screen .user-details .card-body::-webkit-scrollbar,
.card-body::-webkit-scrollbar,
.timeline::-webkit-scrollbar,
body::-webkit-scrollbar,
html::-webkit-scrollbar {
    display: none;
}

/* 确保滚动功能仍然正常工作 */
.screen,
#userinfo-screen.expanded .userinfo-content,
#userinfo-screen .user-details .card-body,
.card-body {
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

/* 平板电脑适配 (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    /* 用户详情侧边栏平板优化 */
    #userinfo-screen {
        width: 320px !important;
        min-width: 320px !important;
    }

    #userinfo-screen.expanded {
        width: 320px;
        min-width: 320px;
    }

    /* 平板端按钮样式优化 */
    .userinfo-toggle-btn.header-toggle {
        width: 30px !important;
        height: 30px !important;
        font-size: 12px !important;
        margin: 0 6px !important;
    }

    /* 主容器布局优化 */
    .main-container {
        gap: 8px;
    }

    /* 导航屏幕优化 */
    #nav-screen {
        width: 300px;
        padding: 18px;
    }

    /* 项目屏幕优化 */
    #project-screen {
        width: 380px;
        padding: 18px;
    }

    /* 审核屏幕优化 */
    #review-screen {
        padding: 18px;
    }
}

/* 移动端适配 */
@media (max-width: 768px) {
    #userinfo-screen {
        width: 100% !important;
        min-width: auto !important;
        background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%) !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    #userinfo-screen.expanded {
        width: 100% !important;
        min-width: auto !important;
    }

    #userinfo-screen .userinfo-content,
    #userinfo-screen .userinfo-header {
        opacity: 1 !important;
        pointer-events: auto !important;
        width: auto !important; /* 移动端恢复宽度 */
        height: auto !important; /* 移动端恢复高度 */
    }

    #userinfo-screen .userinfo-content {
        padding: 0 15px 15px 15px !important;
        max-height: calc(100vh - 150px) !important;
        overflow-y: auto !important;
    }

    #userinfo-screen .userinfo-header {
        padding: 15px !important;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    }

    /* 移动端按钮样式 - 在顶部标题栏 */
    .userinfo-toggle-btn.header-toggle {
        width: 28px !important;
        height: 28px !important;
        font-size: 11px !important;
        margin: 0 6px !important;
        border-width: 1px !important;
        box-shadow: 0 1px 4px rgba(0, 112, 107, 0.3) !important;
    }

    .userinfo-toggle-btn.header-toggle:hover {
        transform: scale(1.05) !important;
    }
}

/* 移除重复的样式定义，使用上面的新样式 */

.file-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 10px;
}

.file-input-wrapper {
    position: relative;
    overflow: hidden;
    display: inline-block;
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 4px;
    background-color: #2ecc71;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
}

.notification.error {
    background-color: #e74c3c;
}

.notification.show {
    display: block;
}

.notification.success {
    background-color: #2ecc71;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 10px;
    vertical-align: middle;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.file-uploading {
    opacity: 0.7;
    position: relative;
}

.file-uploading:after {
    content: "上传中...";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2980b9;
    font-weight: bold;
}

.project-details {
    margin-top: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.detail-item {
    display: flex;
    margin-bottom: 10px;
    font-size: 14px;
}

.detail-label {
    font-weight: bold;
    min-width: 100px;
    color: #555;
}

.detail-value {
    flex: 1;
    color: #333;
}

.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-active {
    background-color: #2ecc71;
}

.status-completed {
    background-color: #2ecc71;
    color: white;
}

.file-preview {
    margin-top: 10px;
    padding: 10px;
    border: 1px dashed #ddd;
    border-radius: 4px;
    background-color: #fafafa;
    display: none;
}

.file-preview img {
    max-width: 100%;
    max-height: 200px;
    display: block;
    margin: 0 auto;
}

.file-preview .file-name {
    text-align: center;
    margin-top: 10px;
    font-size: 14px;
    color: #555;
}

.active-filter {
    box-shadow: 0 0 0 2px white !important;
    transform: scale(1.1);
}

/* Timeline styles */
.timeline {
    list-style: none;
    padding: 20px 0 0 0;
    position: relative;
}

.timeline:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #e9ecef;
    left: 20px;
    margin: 0;
}

.timeline-item {
    margin-bottom: 20px;
    position: relative;
    padding-left: 50px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-icon {
    position: absolute;
    left: 20px;
    top: 0;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #adb5bd;
    /* secondary */
    border: 3px solid #f8f9fa;
    transform: translateX(-50%);
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.timeline-item.completed .timeline-icon {
    background: #198754;
    /* success */
}

.timeline-item.pending .timeline-icon {
    background: #0d6efd;
    /* primary */
}

.timeline-item.not-entered .timeline-icon {
    background: #6c757d;
    /* secondary - 数据未录入状态 */
}

.timeline-item.not-entered .timeline-content {
    background: #f8f9fa;
    border: 1px dashed #dee2e6;
    opacity: 0.8;
}

.timeline-content {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    padding: 15px;
    border-radius: 8px;
    position: relative;
}

.timeline-content h6 {
    font-weight: 600;
    margin-top: 0;
}

.timeline-time {
    font-size: 0.85rem;
    color: #6c757d;
}

.user-details dl {
    margin-bottom: 0;
}

.user-details dt {
    font-weight: 500;
    color: #495057;
    display: flex;
    align-items: center;
}

.user-details dd {
    color: #212529;
}

/* 新增项目按钮优化 */
#add-project-btn {
    background: linear-gradient(135deg, #00706B 0%, #4a9b96 100%);
    border: none;
    border-radius: 12px;
    padding: 12px 20px;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(0, 112, 107, 0.25);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

#add-project-btn:hover {
    background: linear-gradient(135deg, #005a56 0%, #3d8a85 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 112, 107, 0.35);
}

#add-project-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 112, 107, 0.3);
}

#add-project-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

#add-project-btn:active::before {
    width: 100px;
    height: 100px;
}

.modal-header {
    background-color: #2980b9;
    color: white;
}

.modal-header .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* 移动端导航栏 */
.mobile-nav {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    border-top: 1px solid #e0e0e0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    padding: 8px 0;
}

/* 移动端状态指示器 */
.mobile-status-indicator {
    display: none;
    position: fixed;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    flex-direction: column;
    gap: 8px;
    z-index: 999;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #ddd;
    transition: background-color 0.3s ease;
}

.status-dot.active {
    background-color: #00706B;
}

.mobile-nav .nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    touch-action: manipulation;
}

.mobile-nav .nav-item i {
    font-size: 20px;
    margin-bottom: 4px;
    color: #666;
    transition: color 0.3s ease;
}

.mobile-nav .nav-item span {
    font-size: 12px;
    color: #666;
    transition: color 0.3s ease;
}

.mobile-nav .nav-item.active i,
.mobile-nav .nav-item.active span {
    color: #00706B;
}

.mobile-nav .nav-item:hover:not(.active) i,
.mobile-nav .nav-item:hover:not(.active) span {
    color: #3498db;
}

/* 平板电脑专用样式 (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    /* 标题栏平板优化 */
    .header-title {
        padding: 16px 24px;
        font-size: 22px;
    }

    .header-title img {
        width: 45px !important;
        height: 45px !important;
    }

    .header-title .user-info {
        font-size: 13px;
        padding: 6px 12px;
    }

    /* 城市导航平板优化 */
    .city-item {
        margin-bottom: 14px;
    }

    .city-name {
        padding: 12px 16px;
        font-size: 15px;
        border-radius: 8px;
    }

    .stat-badge {
        width: 32px;
        height: 22px;
        font-size: 12px;
        margin-left: 6px;
    }

    .option-item {
        padding: 14px 18px;
        margin-bottom: 8px;
        border-radius: 8px;
    }

    .option-name {
        font-size: 15px;
        margin-bottom: 4px;
    }

    .count-badge {
        width: 26px;
        height: 20px;
        font-size: 11px;
    }

    /* 项目列表平板优化 */
    .project-item {
        padding: 16px 20px;
        padding-left: 26px;
        margin-bottom: 14px;
        border-radius: 12px;
    }

    .project-item::before {
        width: 6px;
    }

    .project-item:hover::before,
    .project-item.active::before {
        width: 8px;
    }

    .project-name {
        font-size: 16px;
        margin-bottom: 10px;
        padding-right: 85px;
    }

    .project-meta {
        font-size: 13px;
        margin-bottom: 7px;
    }

    .project-meta span {
        padding: 3px 10px;
        font-size: 12px;
        border-radius: 7px;
        margin-right: 8px;
        margin-bottom: 5px;
    }

    .project-status {
        padding: 7px 14px;
        font-size: 12px;
        right: 14px;
        top: 14px;
        min-width: 65px;
    }

    /* 搜索容器平板优化 */
    .search-container {
        margin-bottom: 22px;
        gap: 14px;
    }

    .search-input {
        padding: 14px 18px;
        font-size: 15px;
        border-radius: 20px;
    }

    .search-clear {
        right: 18px;
        width: 26px;
        height: 26px;
        font-size: 16px;
    }

    /* 新增按钮平板优化 */
    #add-project-btn {
        padding: 14px 20px;
        font-size: 15px;
        border-radius: 10px;
    }

    /* 分页控件平板优化 */
    .pagination {
        padding: 18px 0;
        gap: 8px;
        margin-top: 22px;
    }

    .page-btn {
        min-width: 38px;
        height: 38px;
        font-size: 14px;
        padding: 10px 14px;
    }

    .page-jump input {
        width: 45px;
        height: 38px;
        font-size: 14px;
    }

    .page-jump button {
        padding: 10px 14px;
        font-size: 14px;
    }

    /* 无选择状态平板优化 */
    .no-selection {
        margin: 35px 25px;
        padding: 25px;
        font-size: 16px;
    }

    /* 审核项目平板优化 */
    .review-item {
        padding: 16px;
        margin-bottom: 16px;
        border-radius: 10px;
    }

    .file-status {
        padding: 7px 14px;
        font-size: 12px;
        border-radius: 8px;
    }

    .reason-box {
        padding: 14px;
        font-size: 15px;
        border-radius: 10px;
    }

    /* 文件操作按钮平板优化 */
    .upload-btn,
    .upload-gp-btn,
    .download-btn,
    .pending-btn {
        padding: 12px 18px;
        font-size: 14px;
        border-radius: 8px;
        min-width: 90px;
    }
}

/* 移动端适配样式 */
@media (max-width: 768px) {
    /* 显示移动端导航 */
    .mobile-nav {
        display: flex;
    }
    /* 基础布局调整 */
    body {
        overflow: auto;
        height: auto;
        min-height: 100vh;
        padding-bottom: 70px; /* 为底部导航栏留出空间 */
    }

    .header-title {
        position: relative;
        padding: 12px 15px;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
    }

    .header-title .header-left {
        display: flex;
        align-items: center;
    }

    .header-title .header-right {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .header-title img {
        width: 35px !important;
        height: 35px !important;
        margin-top: 0 !important;
        margin-right: 8px !important;
    }

    .header-title .user-info {
        font-size: 12px;
        padding: 2px 6px;
        margin-top: 2px;
    }

    .logout-btn {
        padding: 4px 6px;
        font-size: 12px;
    }

    .main-container {
        flex-direction: column;
        height: auto;
        overflow: visible;
        position: relative;
    }

    .screen {
        width: 100% !important;
        height: auto;
        border-right: none;
        border-bottom: none;
        padding: 15px;
        overflow-y: visible;
        display: none; /* 默认隐藏所有屏幕 */
    }

    .screen.active {
        display: block; /* 只显示激活的屏幕 */
    }


    /* 标题样式调整 */
    h3 {
        font-size: 16px;
        margin-bottom: 12px;
        padding-bottom: 8px;
    }

    /* 城市导航优化 */
    .city-item {
        margin-bottom: 12px;
    }

    .city-name {
        padding: 12px 15px;
        font-size: 16px;
        border-radius: 6px;
        touch-action: manipulation;
    }

    .city-stats {
        margin-left: 8px;
    }

    .stat-badge {
        width: 35px;
        height: 25px;
        font-size: 13px;
        margin-left: 6px;
        border-radius: 4px;
    }

    .option-item {
        padding: 14px 18px;
        margin-bottom: 8px;
        border-radius: 8px;
        touch-action: manipulation;
    }

    .option-name {
        font-size: 15px;
        font-weight: 500;
        margin-bottom: 6px;
    }

    .count-badge {
        width: 28px;
        height: 22px;
        font-size: 11px;
        border-radius: 3px;
    }

    .total-type-count,
    .completed-type-count {
        width: 30px;
        height: 22px;
        font-size: 11px;
    }

    .type-stats {
        margin-top: 8px;
        gap: 4px;
    }

    /* 移动端项目列表优化 */
    .project-item {
        padding: 18px 20px;
        padding-left: 28px;
        margin-bottom: 16px;
        border-radius: 16px;
        touch-action: manipulation;
        box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
    }

    /* 移动端删除按钮始终显示 */
    .project-actions {
        opacity: 1 !important;
        transform: translateY(0) !important;
        position: absolute;
        bottom: 16px;
        right: 16px;
    }

    .delete-btn {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }

    .project-item::before {
        width: 6px;
        border-top-left-radius: 16px;
        border-bottom-left-radius: 16px;
    }

    .project-item:hover::before,
    .project-item.active::before {
        width: 10px;
    }

    .project-name {
        font-size: 17px;
        margin-bottom: 12px;
        line-height: 1.4;
        font-weight: 600;
        padding-right: 0; /* 移动端状态标签不在右上角 */
    }

    .project-meta {
        font-size: 14px;
        margin-bottom: 8px;
        display: block;
    }

    .project-meta span {
        display: inline-block;
        margin-right: 8px;
        margin-bottom: 6px;
        padding: 4px 10px;
        font-size: 13px;
        border-radius: 8px;
    }

    .project-status {
        position: static;
        margin-top: 12px;
        font-size: 14px;
        padding: 8px 16px;
        border-radius: 12px;
        display: inline-block;
        min-width: 80px;
        text-align: center;
    }

    /* 移动端搜索框优化 */
    .search-container {
        margin-bottom: 24px;
        flex-direction: row;
        gap: 12px;
        align-items: center;
    }

    .search-input {
        flex: 1;
        padding: 16px 20px;
        font-size: 16px;
        border-radius: 16px;
        border-width: 2px;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
        min-width: 0; /* 确保flex项目可以收缩 */
    }

    .search-input:focus {
        transform: none; /* 移动端不使用transform避免布局问题 */
        box-shadow: 0 0 0 3px rgba(0, 112, 107, 0.15), 0 6px 16px rgba(0, 0, 0, 0.1);
    }

    .search-clear {
        right: 20px;
        font-size: 18px;
        width: 28px;
        height: 28px;
        padding: 0;
    }

    /* 移动端新增按钮优化 */
    #add-project-btn {
        padding: 14px 18px;
        font-size: 15px;
        border-radius: 12px;
        margin-bottom: 0;
        touch-action: manipulation;
        flex-shrink: 0;
        white-space: nowrap;
    }

    /* 分页优化 */
    .pagination {
        flex-wrap: wrap;
        gap: 8px;
        font-size: 14px;
        justify-content: center;
        padding: 15px 0;
    }

    .page-btn {
        min-width: 40px;
        height: 40px;
        font-size: 14px;
        border-radius: 6px;
        touch-action: manipulation;
    }

    .page-jump input {
        width: 50px;
        height: 40px;
        font-size: 14px;
        border-radius: 6px;
    }

    .page-jump button {
        padding: 8px 12px;
        font-size: 14px;
        border-radius: 6px;
        touch-action: manipulation;
    }

    /* 审核状态优化 */
    .review-item {
        padding: 15px;
        margin-bottom: 15px;
        border-radius: 8px;
        touch-action: manipulation;
    }

    .review-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 12px;
    }

    .file-status {
        padding: 6px 12px;
        font-size: 13px;
        border-radius: 6px;
    }

    .reason-box {
        padding: 12px;
        font-size: 15px;
        border-radius: 8px;
        line-height: 1.5;
    }

    /* 文件操作按钮优化 */
    .file-actions {
        gap: 10px;
        margin-top: 15px;
    }

    .upload-btn,
    .upload-gp-btn,
    .download-btn,
    .pending-btn {
        padding: 12px 16px;
        font-size: 14px;
        border-radius: 6px;
        min-width: 80px;
        touch-action: manipulation;
    }

    /* 用户详情优化 */
    .project-details {
        padding: 15px;
        border-radius: 10px;
        margin-top: 15px;
    }

    .detail-item {
        flex-direction: column;
        margin-bottom: 12px;
        font-size: 15px;
    }

    .detail-label {
        min-width: auto;
        margin-bottom: 4px;
        font-size: 14px;
    }

    .detail-value {
        font-size: 15px;
        padding-left: 0;
    }

    /* 时间线优化 */
    .timeline {
        padding: 15px 0 0 0;
    }

    .timeline:before {
        left: 15px;
    }

    .timeline-item {
        padding-left: 40px;
        margin-bottom: 15px;
    }

    .timeline-icon {
        left: 15px;
        width: 20px;
        height: 20px;
        font-size: 10px;
    }

    .timeline-content {
        padding: 12px;
        border-radius: 8px;
    }

    .timeline-content h6 {
        font-size: 15px;
        margin-bottom: 8px;
    }

    .timeline-time {
        font-size: 13px;
    }

    /* 用户详情列表优化 */
    .user-details dt {
        font-size: 14px;
        margin-bottom: 4px;
    }

    .user-details dd {
        font-size: 15px;
        margin-bottom: 12px;
        padding-left: 0;
    }

    /* 通知优化 */
    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        padding: 12px 15px;
        font-size: 15px;
        border-radius: 8px;
        text-align: center;
    }

    /* 文件预览优化 */
    .file-preview {
        padding: 15px;
        border-radius: 8px;
        margin-top: 15px;
    }

    .file-preview img {
        max-height: 250px;
    }

    .file-preview .file-name {
        font-size: 15px;
        margin-top: 12px;
    }

    /* 无选择状态优化 */
    .no-selection {
        font-size: 15px;
        margin-top: 30px;
        padding: 20px;
        line-height: 1.5;
    }

    /* 模态框优化 */
    .modal-dialog {
        margin: 10px;
        max-width: calc(100vw - 20px);
    }

    .modal-header {
        padding: 15px;
    }

    .modal-body {
        padding: 15px;
    }

    .modal-footer {
        padding: 15px;
    }

    /* 表单元素优化 */
    .form-control {
        padding: 12px 15px;
        font-size: 16px;
        border-radius: 8px;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    .btn {
        padding: 12px 20px;
        font-size: 16px;
        border-radius: 8px;
        touch-action: manipulation;
    }

    /* 防止双击缩放 */
    * {
        touch-action: manipulation;
    }

    /* 滚动条优化 */
    ::-webkit-scrollbar {
        width: 6px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* 移动端特殊优化 */
    .mobile-nav .nav-item {
        position: relative;
        overflow: hidden;
    }

    .mobile-nav .nav-item::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background-color: rgba(0, 112, 107, 0.1);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.3s ease, height 0.3s ease;
    }

    .mobile-nav .nav-item:active::before {
        width: 100px;
        height: 100px;
    }

    /* 改善点击反馈 */
    .city-name:active,
    .option-item:active,
    .project-item:active,
    .review-item:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    /* 输入框焦点优化 */
    .search-input:focus,
    .form-control:focus {
        transform: scale(1.02);
        transition: transform 0.2s ease;
    }

    /* 按钮点击反馈 */
    .btn:active,
    .upload-btn:active,
    .download-btn:active,
    .pending-btn:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }

    /* 页面切换动画 */
    .screen {
        transition: opacity 0.3s ease-in-out;
    }

    .screen:not(.active) {
        opacity: 0;
        pointer-events: none;
    }

    .screen.active {
        opacity: 1;
        pointer-events: auto;
    }

    /* 加载状态优化 */
    .loading-container {
        padding: 15px !important;
        border-radius: 10px !important;
    }

    .loading-text {
        font-size: 16px !important;
    }

    /* 改善小屏幕上的可读性 */
    @media (max-width: 480px) {
        .header-title {
            font-size: 16px;
            padding: 10px 15px;
        }

        .header-title img {
            width: 30px !important;
            height: 30px !important;
        }

        .header-title .header-right {
            gap: 5px;
        }

        .header-title .user-info {
            font-size: 11px;
            margin-left: 5px;
            padding: 1px 4px;
        }

        .logout-btn {
            padding: 2px 8px;
            font-size: 11px;
        }

        h3 {
            font-size: 15px;
        }

        .project-name {
            font-size: 15px;
        }

        .city-name {
            font-size: 15px;
            padding: 10px 12px;
        }

        .option-item {
            padding: 12px 14px;
            margin-bottom: 6px;
            border-radius: 6px;
        }

        .option-name {
            font-size: 14px;
            margin-bottom: 5px;
        }

        .count-badge {
            width: 26px;
            height: 20px;
            font-size: 10px;
        }

        .total-type-count,
        .completed-type-count {
            width: 28px;
            height: 20px;
            font-size: 10px;
        }

        .mobile-nav .nav-item i {
            font-size: 18px;
        }

        .mobile-nav .nav-item span {
            font-size: 11px;
        }

        .notification {
            font-size: 14px;
            padding: 10px 12px;
        }
    }

    /* 移动端屏幕切换动画优化 */
    @media (max-width: 768px) {
        .screen {
            transform: translateX(0);
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
        }

        .screen:not(.active) {
            opacity: 0;
            transform: translateX(-20px);
            pointer-events: none;
        }

        .screen.active {
            opacity: 1;
            transform: translateX(0);
            pointer-events: auto;
        }

        /* 移动端导航栏激活状态增强 */
        .mobile-nav .nav-item.active {
            background-color: rgba(0, 112, 107, 0.1);
            border-radius: 8px;
            margin: 0 2px;
        }

        .mobile-nav .nav-item.active::after {
            content: '';
            position: absolute;
            top: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 3px;
            background-color: #00706B;
            border-radius: 0 0 3px 3px;
        }

        /* 移动端返回按钮样式（如果需要） */
        .mobile-back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 40px;
            height: 40px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            border-radius: 50%;
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1001;
            font-size: 18px;
            touch-action: manipulation;
        }

        .mobile-back-btn.show {
            display: flex;
        }

        /* 优化移动端长列表滚动 */
        .screen {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
        }
    }
}

/* 移动端PDF查看器样式 */
.pdf-viewer-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.pdf-viewer-toolbar {
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.pdf-viewer-content {
    flex: 1;
    position: relative;
    min-height: 500px;
}

.pdf-fallback {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (max-width: 768px) {
    .pdf-viewer-toolbar {
        padding: 8px 12px;
    }

    .pdf-viewer-toolbar .btn {
        font-size: 14px;
        padding: 8px 12px;
        flex: 1;
        min-width: 120px;
    }

    .pdf-viewer-content {
        min-height: 400px;
    }

    #pdf-iframe {
        height: 400px !important;
    }

    .modal-fullscreen-sm-down .modal-body {
        padding: 0 !important;
    }

    .modal-fullscreen-sm-down .pdf-viewer-content {
        min-height: calc(100vh - 120px);
    }

    .modal-fullscreen-sm-down #pdf-iframe {
        height: calc(100vh - 120px) !important;
    }
}

/* PDF查看器加载状态 */
.pdf-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
}

.pdf-loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 移动端文件操作按钮增强 */
@media (max-width: 768px) {
    .download-btn {
        position: relative;
        overflow: hidden;
    }

    .download-btn::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.3s ease, height 0.3s ease;
    }

    .download-btn:active::after {
        width: 100px;
        height: 100px;
    }
}

/* 平板电脑交互优化和特殊效果 */
@media (min-width: 769px) and (max-width: 1024px) {
    /* 触摸友好的交互区域 */
    .city-name,
    .option-item,
    .project-item,
    .review-item {
        cursor: pointer;
        -webkit-tap-highlight-color: rgba(0, 112, 107, 0.1);
    }

    /* 平板专用悬停效果 */
    .project-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 112, 107, 0.15);
    }

    .city-name:hover,
    .option-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    /* 按钮悬停效果优化 */
    .page-btn:hover:not(.disabled) {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 112, 107, 0.2);
    }

    #add-project-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 112, 107, 0.3);
    }

    /* 搜索框聚焦效果 */
    .search-input:focus {
        transform: translateY(-2px);
        box-shadow: 0 0 0 3px rgba(0, 112, 107, 0.15), 0 6px 20px rgba(0, 0, 0, 0.1);
    }

    /* 平板专用动画时长调整 */
    .project-item,
    .city-name,
    .option-item,
    .page-btn,
    #add-project-btn,
    .search-input {
        transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* 滚动条样式优化 */
    .screen::-webkit-scrollbar {
        width: 8px;
        display: block;
    }

    .screen::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .screen::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
    }

    .screen::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* 平板专用字体优化 */
    body {
        font-size: 15px;
        line-height: 1.5;
    }

    h3 {
        font-size: 18px;
        margin-bottom: 16px;
        padding-bottom: 12px;
    }

    /* 通知样式平板优化 */
    .notification {
        top: 24px;
        right: 24px;
        padding: 16px 28px;
        font-size: 15px;
        border-radius: 10px;
        max-width: 400px;
    }

    /* 模态框平板优化 */
    .modal-dialog {
        margin: 20px auto;
        max-width: 600px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 20px;
    }

    /* 表单控件平板优化 */
    .form-control {
        padding: 14px 18px;
        font-size: 15px;
        border-radius: 10px;
    }

    .btn {
        padding: 14px 24px;
        font-size: 15px;
        border-radius: 10px;
    }

    /* 时间线平板优化 */
    .timeline {
        padding: 20px 0 0 0;
    }

    .timeline:before {
        left: 24px;
        width: 4px;
    }

    .timeline-item {
        padding-left: 50px;
        margin-bottom: 20px;
    }

    .timeline-icon {
        left: 24px;
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .timeline-content {
        padding: 16px;
        border-radius: 10px;
    }

    /* 用户详情平板优化 */
    .user-details dt {
        font-size: 15px;
        margin-bottom: 6px;
    }

    .user-details dd {
        font-size: 16px;
        margin-bottom: 14px;
    }

    .project-details {
        padding: 18px;
        border-radius: 12px;
        margin-top: 18px;
    }

    .detail-item {
        margin-bottom: 14px;
        font-size: 15px;
    }

    .detail-label {
        min-width: 120px;
        font-size: 14px;
        margin-bottom: 6px;
    }

    .detail-value {
        font-size: 16px;
    }
}

/* 平板横屏模式特殊优化 */
@media (min-width: 769px) and (max-width: 1024px) and (orientation: landscape) {
    /* 横屏时优化布局 */
    .main-container {
        gap: 12px;
    }

    #nav-screen {
        width: 280px;
    }

    #project-screen {
        width: 360px;
    }

    /* 横屏时项目卡片更紧凑 */
    .project-item {
        padding: 14px 18px;
        padding-left: 24px;
        margin-bottom: 12px;
    }

    .project-name {
        font-size: 15px;
        margin-bottom: 8px;
    }

    .project-meta {
        font-size: 12px;
        margin-bottom: 6px;
    }

    .project-status {
        padding: 6px 12px;
        font-size: 11px;
        right: 12px;
        top: 12px;
    }
}

/* 平板竖屏模式特殊优化 */
@media (min-width: 769px) and (max-width: 1024px) and (orientation: portrait) {
    /* 竖屏时给更多空间 */
    #nav-screen {
        width: 320px;
    }

    #project-screen {
        width: 400px;
    }

    /* 竖屏时项目卡片更宽松 */
    .project-item {
        padding: 18px 22px;
        padding-left: 28px;
        margin-bottom: 16px;
    }

    .project-name {
        font-size: 17px;
        margin-bottom: 12px;
    }

    .project-meta {
        font-size: 14px;
        margin-bottom: 8px;
    }

    .project-status {
        padding: 8px 16px;
        font-size: 13px;
        right: 16px;
        top: 16px;
    }
}