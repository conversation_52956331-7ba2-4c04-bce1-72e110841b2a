<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>地市统计数据 - 华宇收资管理系统</title>
    <link rel="stylesheet" href="./bootstrap-icons.css">
    <link rel="stylesheet" href="./mycss.css">
    <link href="./bootstrap.min.css" rel="stylesheet">
    <style>
        /* 统计页面专用样式 - 覆盖主系统的布局限制 */
        body {
            overflow: auto !important;
            height: auto !important;
        }

        .stats-container {
            padding: 20px;
            background-color: #f8f9fa;
            min-height: calc(100vh - 80px);
        }

        .stats-header {
            background: linear-gradient(135deg, #00706b, #4a9b96);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0, 112, 107, 0.2);
        }

        .stats-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stats-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }

        .city-stats-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .table-header {
            background-color: #00706b;
            color: white;
            padding: 20px;
            font-size: 18px;
            font-weight: bold;
        }

        .stats-table {
            width: 100%;
            border-collapse: collapse;
        }

        .stats-table th {
            background-color: #f8f9fa;
            color: #333;
            padding: 15px 12px;
            text-align: center;
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
            font-size: 14px;
        }

        .stats-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
            color: #333;
        }

        .stats-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .city-name {
            font-weight: 600;
            color: #00706b;
            text-align: left !important;
            padding-left: 20px !important;
        }

        .type-name {
            color: #495057;
            font-weight: 500;
            text-align: left !important;
            padding-left: 30px !important;
        }

        /* 统计徽章样式 */
        .stat-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            color: white;
            min-width: 24px;
            text-align: center;
        }

        .total-badge { background-color: #6c757d; }
        .completed-badge { background-color: #28a745; }
        .pending-submit-badge { background-color: #dc3545; }
        .rejected-badge { background-color: #ffc107; color: #212529; }
        .pending-badge { background-color: #007bff; }

        /* 加载状态 */
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 300px;
            flex-direction: column;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #00706b;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-container {
            text-align: center;
            padding: 50px;
            color: #dc3545;
        }

        .error-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .stats-container {
                padding: 10px;
            }

            .stats-header {
                padding: 20px;
            }

            .stats-title {
                font-size: 24px;
            }

            .stats-table th,
            .stats-table td {
                padding: 6px 4px;
                font-size: 11px;
                color: #333;
            }

            .stats-table th {
                font-size: 10px;
                line-height: 1.2;
                word-break: break-word;
            }

            .city-name {
                padding-left: 10px !important;
            }

            .type-name {
                padding-left: 15px !important;
            }

            .progress-text {
                font-size: 10px !important;
            }
        }

        /* 汇总卡片样式 */
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .summary-card-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .summary-card-value {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
            color: black;
        }

        .summary-card-label {
            color: #6c757d;
            font-size: 14px;
        }

        .summary-card.total { border-left: 4px solid #6c757d; }
        .summary-card.completed { border-left: 4px solid #28a745; }
        .summary-card.pending-submit { border-left: 4px solid #dc3545; }
        .summary-card.rejected { border-left: 4px solid #ffc107; }
        .summary-card.pending { border-left: 4px solid #007bff; }

        /* 分类表格样式 */
        .stats-tables-container {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .photovoltaic-header {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .low-voltage-header {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .high-voltage-header {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        /* 空数据行样式 */
        .empty-data-row {
            color: #6c757d;
            font-style: italic;
        }

        .empty-data-row .stat-badge {
            background-color: #e9ecef;
            color: #6c757d;
        }

        /* 进度条文字样式 */
        .progress {
            position: relative;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            font-weight: 600;
            z-index: 10;
            text-shadow: 0 0 3px rgba(0,0,0,0.3);
        }
    </style>
</head>

<body>
    <!-- 通知区域 -->
    <div class="notification" id="notification"></div>

    <!-- 标题栏 -->
    <div class="header-title">
        <div class="header-left">
            <img src="./logo.png" alt="logo" style="width: 50px; height: 50px; margin-top: -11px;margin-right: 8px;">
            地市统计数据
        </div>
        <div class="header-right">
            <div class="user-info" id="user-info">
                <!-- 用户信息将通过JavaScript动态显示 -->
            </div>
            <button class="logout-btn" id="logout-btn" title="退出登录">
                <i class="bi bi-box-arrow-right"></i>
            </button>
            <button class="btn btn-outline-primary btn-sm ms-2" onclick="window.location.href='./hysz.html'" title="返回主页">
                <i class="bi bi-house-fill"></i>
            </button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="stats-container">
        <!-- 页面标题 -->
        <div class="stats-header">
            <div class="stats-title">
                <i class="bi bi-bar-chart-fill me-3"></i>
                地市统计数据总览
            </div>
            <div class="stats-subtitle">
                实时展示各地市项目统计信息，按照统计徽章规则分类显示
            </div>
        </div>

        <!-- 汇总统计卡片 -->
        <div class="summary-cards" id="summary-cards">
            <!-- 汇总卡片将通过JavaScript动态生成 -->
        </div>

        <!-- 分类统计表格 -->
        <div class="stats-tables-container">
            <!-- 光伏项目统计表格 -->
            <div class="city-stats-table mb-4">
                <div class="table-header photovoltaic-header">
                    <i class="bi bi-sun-fill me-2"></i>
                    光伏项目统计
                </div>
                <div id="photovoltaic-stats-content">
                    <!-- 光伏项目数据将在这里显示 -->
                </div>
            </div>

            <!-- 低压项目统计表格 -->
            <div class="city-stats-table mb-4">
                <div class="table-header low-voltage-header">
                    <i class="bi bi-lightning-fill me-2"></i>
                    低压项目统计
                </div>
                <div id="low-voltage-stats-content">
                    <!-- 低压项目数据将在这里显示 -->
                </div>
            </div>

            <!-- 高压项目统计表格 -->
            <div class="city-stats-table mb-4">
                <div class="table-header high-voltage-header">
                    <i class="bi bi-plug-fill me-2"></i>
                    高压项目统计
                </div>
                <div id="high-voltage-stats-content">
                    <!-- 高压项目数据将在这里显示 -->
                </div>
            </div>

            <!-- 加载状态容器 -->
            <div id="loading-container" style="display: none;">
                <div class="loading-container">
                    <div class="loading-spinner"></div>
                    <div>正在加载统计数据...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="./bootstrap.bundle.min.js"></script>
    <script>
        // API基础URL
        const API_BASE_URL = 'http://**************:5000';
        
        // 获取token和用户信息
        const token = localStorage.getItem('access_token');
        const user_city = localStorage.getItem('city');
        const user_name = localStorage.getItem('username');
        
        // 检查登录状态 - 暂时注释掉用于测试
        // if (!token) {
        //     window.location.href = './login.html';
        // }

        // 显示用户信息
        function displayUserInfo() {
            const userInfoElement = document.getElementById('user-info');
            if (userInfoElement && user_city && user_name) {
                let displayText = '';
                if (user_city === 'VIP') {
                    displayText = `超级管理员 - ${user_name}`;
                } else {
                    displayText = `${user_city} - ${user_name}`;
                }
                userInfoElement.textContent = displayText;
            } else {
                if (userInfoElement) {
                    userInfoElement.textContent = '未登录';
                }
            }
        }

        // 退出登录功能
        function logout() {
            localStorage.removeItem('access_token');
            localStorage.removeItem('city');
            localStorage.removeItem('username');
            showNotification('已退出登录');
            setTimeout(() => {
                window.location.href = './login.html';
            }, 1000);
        }

        // 绑定退出登录按钮事件
        document.getElementById('logout-btn').addEventListener('click', function() {
            if (confirm('确定要退出登录吗？')) {
                logout();
            }
        });

        // 显示通知
        function showNotification(message, isError = false) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${isError ? 'error' : 'success'} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 文件状态常量定义（与myjs.js保持一致）
        const FILE_STATE = {
            PENDING: 0,        // 待审核
            APPROVED: 1,       // 已通过
            REJECTED: 2,       // 已退回
            PENDING_SUBMIT: 3  // 待提交
        };

        // 定义文件模板（与myjs.js保持完全一致）
        const fileTemplates = {
            "高压用户": [
                { file_id: 1, name: "用电登记表（包括一次性告知书）" },
                { file_id: 2, name: "用电人有效身份证件" },
                { file_id: 3, name: "用电地址物权证件" },
                { file_id: 4, name: "电工程项目批准文件" },
                { file_id: 5, name: "授权委托书（根据是否有委托产生而定）" },
                { file_id: 6, name: "现场勘查单" },
                { file_id: 7, name: "供电方案答复单" },
                { file_id: 8, name: "设计资质证书（复印件）*" },
                { file_id: 9, name: "承装（修、试）电力设施许可证（复印件）、试验单位资质证明（复印件）*" },
                { file_id: 10, name: "竣工归档资料（竣工图纸-蓝图）*" },
                { file_id: 16, name: "竣工归档资料（电气设备出厂合格证书）*" },
                { file_id: 17, name: "竣工归档资料（电气设备交接试验记录）*" },
                { file_id: 11, name: "电能计量装置装（拆）单" },
                { file_id: 12, name: "受电工程竣工报验单" },
                { file_id: 13, name: "受电工程竣工检验意见单" },
                { file_id: 14, name: "供用电合同及其附件、相关补充协议" },
                { file_id: 15, name: "送（停）电单" }
            ],
            "低压用户": [
                { file_id: 1, name: "用电登记表（包含一次性告知书）" },
                { file_id: 2, name: "小微企业截图" },
                { file_id: 3, name: "用电人有效身份证件" },
                { file_id: 4, name: "用电地址物权证件" },
                { file_id: 5, name: "授权委托书（根据是否有委托产生而定）" },
                { file_id: 6, name: "低压现场勘查单" },
                { file_id: 7, name: "计量装置装（拆）单" },
                { file_id: 8, name: "现场装表照片" },
                { file_id: 9, name: "送（停）电单" },
                { file_id: 10, name: "供用电合同" }
            ],
            "光伏低压自然人": [
                { file_id: 1, name: "分布式电源项目并网申请表" },
                { file_id: 2, name: "自投承诺书" },
                { file_id: 3, name: "居民身份证" },
                { file_id: 4, name: "产权合法证明" },
                { file_id: 5, name: "分布式电源项目现场勘察意见单" },
                { file_id: 6, name: "分布式电源项目接入系统方案项目业主（用户）确认单" },
                { file_id: 7, name: "购售电合同及附件" },
                { file_id: 8, name: "并网验收意见单" },
                { file_id: 9, name: "电能计量装置装(拆)单" },
                { file_id: 10, name: "设备（包括光伏组件、逆变器）购置发票*" },
                { file_id: 11, name: "光伏项目备案资料" },
                { file_id: 12, name: "安全生产许可证" },
                { file_id: 13, name: "电气设备交接试验记录" },
                { file_id: 14, name: "建筑企业资质证书" },
                { file_id: 15, name: "主要设备技术参数、型式认证报告或" },
                { file_id: 16, name: "施工单位资质，承装（修、试）电力设施许可证（复印件）、试验单位资质证明（复印件）*" },
                { file_id: 17, name: "现场装表照片" },
                { file_id: 18, name: "低压分布式光伏接入系统方案（本地留存）" }
            ],
            "光伏低压非自然人": [
                { file_id: 1, name: "分布式电源项目并网申请表" },
                { file_id: 2, name: "居民身份证" },
                { file_id: 3, name: "营业执照" },
                { file_id: 4, name: "租赁协议、租赁方产权证明" },
                { file_id: 5, name: "光伏项目备案资料" },
                { file_id: 6, name: "分布式电源项目现场勘察意见单" },
                { file_id: 7, name: "分布式电源项目接入系统方案项目业主（用户）确认单" },
                { file_id: 8, name: "并网检验申请单" },
                { file_id: 9, name: "并网验收意见单" },
                { file_id: 10, name: "电能计量装置装(拆)单" },
                { file_id: 11, name: "设备（包括光伏组件、逆变器）购置发票*" },
                { file_id: 12, name: "安全生产许可证" },
                { file_id: 13, name: "电气设备交接试验记录" },
                { file_id: 14, name: "建筑企业资质证书" },
                { file_id: 15, name: "主要设备技术参数、型式认证报告或" },
                { file_id: 16, name: "施工单位资质，承装（修、试）电力设施许可证（复印件）、试验单位资质证明（复印件）*" },
                { file_id: 17, name: "现场装表照片" },
                { file_id: 18, name: "并网调度协议" },
                { file_id: 19, name: "购售电合同及附件" },
                { file_id: 20, name: "低压分布式光伏接入系统方案（本地留存）" }
            ],
            "光伏高压": [
                { file_id: 1, name: "政府职能部门有关本项目立项的批复*" },
                { file_id: 2, name: "电网承载力评估查询结果图*" },
                { file_id: 3, name: "分布式电源项目并网申请表*" },
                { file_id: 4, name: "居民身份证" },
                { file_id: 5, name: "营业执照" },
                { file_id: 6, name: "产权证*" },
                { file_id: 7, name: "租赁协议、租赁方产权证明*" },
                { file_id: 8, name: "分布式电源项目现场勘察意见单*" },
                { file_id: 9, name: "分布式电源项目接入系统方案项目业主（用户）确认单*" },
                { file_id: 10, name: "分布式电源项目接入系统方案" },
                { file_id: 11, name: "关于XXX项目接入电网意见的函" },
                { file_id: 12, name: "竣工归档资料(包括竣工图纸、电气设备出厂合格证书、电气设备交接试验记录)*" },
                { file_id: 13, name: "客户工程竣工检验意见单*" },
                { file_id: 14, name: "设备（包括光伏组件、逆变器）购置发票*" },
                { file_id: 15, name: "计量装置装（拆）单*" },
                { file_id: 16, name: "并网验收意见单*" },
                { file_id: 17, name: "购售电合同及附件" },
                { file_id: 18, name: "高压电能计量装接单" },
                { file_id: 19, name: "表计示数照片" },
                { file_id: 20, name: "计量箱封印(锁)及外观" }
            ]
        };

        // 华宇高压用户使用与高压用户相同的模板
        fileTemplates.华宇高压用户 = fileTemplates.高压用户;

        // 获取项目类型所需的文件数量（与myjs.js完全一致）
        function getRequiredFileCount(projectType) {
            const template = fileTemplates[projectType];
            return template ? template.length : null;
        }

        // 计算项目状态统计（修复后的逻辑，与myjs.js保持一致）
        function calculateProjectStats(projects) {
            const stats = {
                total: projects.length,
                completed: 0,
                pendingSubmit: 0,
                rejected: 0,
                pending: 0
            };

            projects.forEach(project => {
                // 如果项目已完成，直接计入已完成
                if (project.bao_state !== false) {
                    stats.completed++;
                    return;
                }

                // 检查项目级别的状态字段（来自服务器）
                let projectLevelPendingSubmit = project.is_submit === true;
                let projectLevelRejected = project.is_reject === true;
                let projectLevelPending = project.is_approve === true;

                // 检查文件级别的状态
                let fileLevelPendingSubmit = false;
                let fileLevelRejected = false;
                let fileLevelPending = false;

                // 确保 project.files 存在
                if (!project.files) {
                    project.files = [];
                }

                if (project.files.length > 0) {
                    // 检查文件状态
                    fileLevelRejected = project.files.some(f => f.file_state === FILE_STATE.REJECTED);
                    fileLevelPending = project.files.some(f => f.file_state === FILE_STATE.PENDING);
                }

                // 检查是否还有文件需要提交（基于文件数量不足）
                const projectType = project.bao_lx;
                const requiredFileCount = getRequiredFileCount(projectType);

                if (requiredFileCount) {
                    // 如果已上传文件数量小于要求数量，则存在待提交状态
                    fileLevelPendingSubmit = project.files.length < requiredFileCount;
                }

                // 综合判断：项目级别状态优先，文件级别状态作为补充
                let hasPendingSubmit = projectLevelPendingSubmit || fileLevelPendingSubmit;
                let hasRejected = projectLevelRejected || fileLevelRejected;
                let hasPending = projectLevelPending || fileLevelPending;

                // 按照优先级统计：待提交 > 已退回 > 审核中
                if (hasPendingSubmit) {
                    stats.pendingSubmit++;
                } else if (hasRejected) {
                    stats.rejected++;
                } else if (hasPending) {
                    stats.pending++;
                }
            });

            return stats;
        }

        // 生成汇总统计卡片
        function generateSummaryCards(allStats) {
            const summaryCards = document.getElementById('summary-cards');

            const cards = [
                { key: 'total', label: '总项目数', icon: 'bi-folder-fill', class: 'total' },
                { key: 'completed', label: '已完成', icon: 'bi-check-circle-fill', class: 'completed' },
                { key: 'pendingSubmit', label: '待提交', icon: 'bi-exclamation-circle-fill', class: 'pending-submit' },
                { key: 'rejected', label: '已退回', icon: 'bi-x-circle-fill', class: 'rejected' },
                { key: 'pending', label: '审核中', icon: 'bi-clock-fill', class: 'pending' }
            ];

            summaryCards.innerHTML = cards.map(card => {
                let iconColor = '#6c757d'; // 默认灰色
                switch(card.class) {
                    case 'total': iconColor = '#6c757d'; break;
                    case 'completed': iconColor = '#28a745'; break;
                    case 'pending-submit': iconColor = '#dc3545'; break;
                    case 'rejected': iconColor = '#ffc107'; break;
                    case 'pending': iconColor = '#007bff'; break;
                }

                return `
                    <div class="summary-card ${card.class}">
                        <div class="summary-card-icon ${card.icon}" style="color: ${iconColor};"></div>
                        <div class="summary-card-value">${allStats[card.key]}</div>
                        <div class="summary-card-label">${card.label}</div>
                    </div>
                `;
            }).join('');
        }

        // 项目类型分类定义
        const projectCategories = {
            photovoltaic: {
                name: '光伏项目',
                types: ['光伏低压自然人', '光伏低压非自然人', '光伏高压'],
                containerId: 'photovoltaic-stats-content'
            },
            lowVoltage: {
                name: '低压项目',
                types: ['低压用户'],
                containerId: 'low-voltage-stats-content'
            },
            highVoltage: {
                name: '高压项目',
                types: ['高压用户', '华宇高压用户'],
                containerId: 'high-voltage-stats-content'
            }
        };

        // 生成单个分类表格
        function generateCategoryTable(projectsData, category) {
            const allCities = Object.keys(projectsData);

            let tableHTML = `
                <table class="stats-table">
                    <thead>
                        <tr>
                            <th style="width: 16%;">单位</th>
                            <th style="width: 11%;">项目总数</th>
                            <th style="width: 11%;">已完成</th>
                            <th style="width: 11%;">待提交</th>
                            <th style="width: 11%;">已退回</th>
                            <th style="width: 11%;">审核中</th>
                            <th style="width: 14%;">项目完成率</th>
                            <th style="width: 15%;">项目上传率</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            // 遍历所有城市
            allCities.forEach(cityName => {
                const cityData = projectsData[cityName];

                // 计算该城市在当前分类下的统计
                const cityStats = {
                    total: 0,
                    completed: 0,
                    pendingSubmit: 0,
                    rejected: 0,
                    pending: 0
                };

                // 只统计当前分类下的项目类型
                category.types.forEach(typeName => {
                    if (cityData[typeName]) {
                        const projects = cityData[typeName];
                        const typeStats = calculateProjectStats(projects);

                        cityStats.total += typeStats.total;
                        cityStats.completed += typeStats.completed;
                        cityStats.pendingSubmit += typeStats.pendingSubmit;
                        cityStats.rejected += typeStats.rejected;
                        cityStats.pending += typeStats.pending;
                    }
                });

                // 计算完成率
                const cityCompletionRate = cityStats.total > 0 ?
                    ((cityStats.completed / cityStats.total) * 100).toFixed(1) : '0.0';

                // 计算上传率（审核中文件数量+已退回文件数量+已完成项目数量)/项目总数
                const cityUploadRate = cityStats.total > 0 ?
                    (((cityStats.pending + cityStats.rejected + cityStats.completed) / cityStats.total) * 100).toFixed(1) : '0.0';

                // 判断是否为空数据行
                const isEmptyRow = cityStats.total === 0;
                const rowClass = isEmptyRow ? 'empty-data-row' : '';

                // 添加城市行
                tableHTML += `
                    <tr class="${rowClass}">
                        <td class="city-name">${cityName}</td>
                        <td><span class="stat-badge total-badge">${cityStats.total}</span></td>
                        <td><span class="stat-badge completed-badge">${cityStats.completed}</span></td>
                        <td><span class="stat-badge pending-submit-badge">${cityStats.pendingSubmit}</span></td>
                        <td><span class="stat-badge rejected-badge">${cityStats.rejected}</span></td>
                        <td><span class="stat-badge pending-badge">${cityStats.pending}</span></td>
                        <td>
                            ${isEmptyRow ? '<span class="text-muted">-</span>' : `
                                <div class="progress" style="height: 20px; position: relative;">
                                    <div class="progress-bar bg-success" role="progressbar"
                                         style="width: ${cityCompletionRate}%"
                                         aria-valuenow="${cityCompletionRate}"
                                         aria-valuemin="0"
                                         aria-valuemax="100">
                                    </div>
                                    <div class="progress-text" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: ${parseFloat(cityCompletionRate) > 50 ? 'white' : '#333'}; font-size: 12px; font-weight: 600; z-index: 10;">
                                        ${cityCompletionRate}%
                                    </div>
                                </div>
                            `}
                        </td>
                        <td>
                            ${isEmptyRow ? '<span class="text-muted">-</span>' : `
                                <div class="progress" style="height: 20px; position: relative;">
                                    <div class="progress-bar bg-info" role="progressbar"
                                         style="width: ${cityUploadRate}%"
                                         aria-valuenow="${cityUploadRate}"
                                         aria-valuemin="0"
                                         aria-valuemax="100">
                                    </div>
                                    <div class="progress-text" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: ${parseFloat(cityUploadRate) > 50 ? 'white' : '#333'}; font-size: 12px; font-weight: 600; z-index: 10;">
                                        ${cityUploadRate}%
                                    </div>
                                </div>
                            `}
                        </td>
                    </tr>
                `;
            });

            tableHTML += `
                    </tbody>
                </table>
            `;

            return tableHTML;
        }

        // 生成统计表格
        function generateStatsTable(projectsData) {
            console.log('开始生成统计表格，数据:', projectsData);

            // 计算总体统计
            const allStats = {
                total: 0,
                completed: 0,
                pendingSubmit: 0,
                rejected: 0,
                pending: 0
            };

            // 计算总体统计数据
            Object.keys(projectsData).forEach(cityName => {
                const cityData = projectsData[cityName];
                Object.keys(cityData).forEach(typeName => {
                    const projects = cityData[typeName];
                    const typeStats = calculateProjectStats(projects);

                    allStats.total += typeStats.total;
                    allStats.completed += typeStats.completed;
                    allStats.pendingSubmit += typeStats.pendingSubmit;
                    allStats.rejected += typeStats.rejected;
                    allStats.pending += typeStats.pending;
                });
            });

            // 生成各分类表格
            console.log('开始生成各分类表格');
            Object.keys(projectCategories).forEach(categoryKey => {
                const category = projectCategories[categoryKey];
                console.log(`生成${category.name}表格`);
                const container = document.getElementById(category.containerId);
                if (!container) {
                    console.error(`找不到容器: ${category.containerId}`);
                    return;
                }
                const tableHTML = generateCategoryTable(projectsData, category);
                console.log(`${category.name}表格HTML长度:`, tableHTML.length);
                container.innerHTML = tableHTML;
            });

            // 生成汇总卡片
            console.log('生成汇总卡片，统计数据:', allStats);
            generateSummaryCards(allStats);
        }

        // 显示错误信息
        function showError(message) {
            // 隐藏所有表格，显示错误信息
            Object.keys(projectCategories).forEach(categoryKey => {
                const category = projectCategories[categoryKey];
                const container = document.getElementById(category.containerId);
                container.innerHTML = `
                    <div class="error-container">
                        <div class="error-icon">
                            <i class="bi bi-exclamation-triangle-fill"></i>
                        </div>
                        <h5>加载失败</h5>
                        <p>${message}</p>
                        <button class="btn btn-primary" onclick="loadStatsData()">
                            <i class="bi bi-arrow-clockwise me-2"></i>重新加载
                        </button>
                    </div>
                `;
            });
        }

        // 加载统计数据
        function loadStatsData() {
            // 显示加载状态
            Object.keys(projectCategories).forEach(categoryKey => {
                const category = projectCategories[categoryKey];
                const container = document.getElementById(category.containerId);
                container.innerHTML = `
                    <div class="loading-container">
                        <div class="loading-spinner"></div>
                        <div>正在加载${category.name}数据...</div>
                    </div>
                `;
            });

            // 调用baos接口获取数据
            fetch(`${API_BASE_URL}/baos`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.cont) {
                    generateStatsTable(data.cont);
                    showNotification('统计数据加载成功');
                } else {
                    throw new Error('返回数据格式错误');
                }
            })
            .catch(error => {
                console.error('加载统计数据失败:', error);
                showError('无法获取统计数据，请检查网络连接或稍后重试');
                showNotification('加载统计数据失败', true);
            });
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            displayUserInfo();

            // 如果没有token，显示演示数据
            if (!token) {
                console.log('没有token，显示演示数据');
                showDemoData();
            } else {
                loadStatsData();
            }
        });

        // 显示演示数据
        function showDemoData() {
            console.log('开始显示演示数据');
            const demoData = {
                "驻马店": {
                    "光伏低压自然人": [
                        { bao_state: false, files: [{ file_state: 3 }] },
                        { bao_state: true, files: [] },
                        { bao_state: false, files: [{ file_state: 0 }] }
                    ],
                    "低压用户": [
                        { bao_state: true, files: [] },
                        { bao_state: false, files: [{ file_state: 2 }] }
                    ],
                    "高压用户": [
                        { bao_state: true, files: [] },
                        { bao_state: false, files: [{ file_state: 0 }] },
                        { bao_state: false, files: [{ file_state: 3 }] }
                    ]
                },
                "信阳": {
                    "光伏低压自然人": [],
                    "低压用户": [
                        { bao_state: true, files: [] }
                    ],
                    "高压用户": [
                        { bao_state: true, files: [] },
                        { bao_state: true, files: [] }
                    ]
                },
                "南阳": {
                    "光伏低压自然人": [
                        { bao_state: false, files: [{ file_state: 3 }] }
                    ],
                    "低压用户": [
                        { bao_state: false, files: [{ file_state: 2 }] },
                        { bao_state: true, files: [] }
                    ],
                    "高压用户": [
                        { bao_state: true, files: [] },
                        { bao_state: false, files: [{ file_state: 0 }] },
                        { bao_state: false, files: [{ file_state: 3 }] },
                        { bao_state: true, files: [] }
                    ]
                }
            };

            console.log('演示数据:', demoData);
            generateStatsTable(demoData);
            showNotification('演示数据加载完成');
        }
    </script>
</body>

</html>
