<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传统计</title>
    <link href="./bootstrap.min.css" rel="stylesheet">
    <link href="./bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .stats-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin: 0;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .daily-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            overflow: hidden;
        }
        .daily-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 1rem;
            font-weight: bold;
        }
        .file-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s;
        }
        .file-item:hover {
            background-color: #f8f9fa;
        }
        .file-item:last-child {
            border-bottom: none;
        }
        .project-info {
            font-size: 0.9em;
            color: #6c757d;
        }
        .badge-custom {
            font-size: 0.8em;
        }
        .filter-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .loading {
            text-align: center;
            padding: 3rem;
        }
        .no-data {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        .project-header {
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .project-header:hover {
            background-color: #e9ecef !important;
        }
        .project-files {
            display: none;
        }
        .project-files.show {
            display: block;
        }
        .collapse-icon {
            transition: transform 0.2s;
        }
        .collapse-icon.expanded {
            transform: rotate(90deg);
        }
        
        /* 确保统计概览区域的行为 */
        #summarySe:tion .row {
            margin-left: 0;
            margin-right: 0;
        }
        
        #summarySection [class*="col-"] {
            padding-left: 0. rrem;
            padding-right: 0.o5rem;
        }
        
        /* 强制桌面端一行显示 */
        @media (min-width: 1200px) {
            #summarySection {
                display: flex !important;
                flex-wrap: nowrap;
                gap: 1rem;
            }
            
            #summarySection > div {
                flex: 1;
                min-width: 0;
            }
            
            #summarySection > div:last-child {
                flex: 2;
            }
        }
        .project-name-display {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            min-width: 0; /* 允许flex项目收缩到内容以下 */
        }
        
        .latest-file-time {
            font-size: 0.85em;
            color: #6c757d;
            white-space: nowrap; /* 防止时间换行 */
            min-width: max-content; /* 确保时间区域有足够空间 */
        }
        
        /* 确保右侧时间区域不被挤压 */
        .project-header .text-end {
            min-width: 120px; /* 为时间显示预留最小宽度 */
        }
        
        /* 移动端优化 */
        @media (max-width: 767px) {
            .project-name-display {
                max-width: 100px;
            }
            
            .project-header .text-end {
                min-width: 100px;
            }
            
            /* 在小屏幕上隐藏工单号以节省空间 */
            .project-header .text-muted {
                display: none;
            }
        }
        
        /* 小屏移动端进一步优化 */
        @media (max-width: 480px) {
            .project-name-display {
                max-width: 60px;
            }
            
            .project-header .text-end {
                min-width: 80px;
            }
            
            /* 在极小屏幕上简化显示 */
            .badge-custom {
                font-size: 0.7em;
                padding: 0.2em 0.4em;
            }
        }
        
        /* 确保flex容器正确处理溢出 */
        .min-width-0 {
            min-width: 0;
        }
        
        .flex-shrink-0 {
            flex-shrink: 0;
        }
        
        /* 确保统计概览区域的行为 */
        #summarySection .row {
            margin-left: 0;
            margin-right: 0;
        }

        #summarySection [class*="col-"] {
            padding-left: 0.75rem;
            padding-right: 0.75rem;
        }

        /* 强制桌面端一行显示 */
        @media (min-width: 1200px) {
            #summarySection {
                display: flex !important;
                flex-wrap: nowrap;
                gap: 1rem;
            }

            #summarySection > div {
                flex: 1;
                min-width: 0;
            }

            #summarySection > div:last-child {
                flex: 2;
            }
        }

        /* 可点击徽章样式 */
        .clickable-badge {
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .clickable-badge:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .clickable-badge:active {
            transform: scale(0.98);
        }

        /* 模态框样式 */
        .project-detail-modal {
            display: none;
            position: fixed;
            z-index: 9999; /* 提高z-index确保在最顶层 */
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(3px);
        }

        .modal-content-custom {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease-out;
            position: relative; /* 确保相对定位 */
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-bottom: none;
            position: relative; /* 确保相对定位 */
        }

        .modal-body-custom {
            padding: 1.5rem;
            max-height: 60vh;
            overflow-y: auto;
        }

        /* 移除模态框底部按钮区域 */
        .modal-footer-custom {
            display: none; /* 隐藏底部按钮区域 */
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            position: absolute; /* 绝对定位确保不被遮挡 */
            top: 1rem;
            right: 1rem;
            opacity: 0.8;
            transition: opacity 0.2s;
            z-index: 10000; /* 确保关闭按钮在最顶层 */
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .close-btn:hover {
            opacity: 1;
            background-color: rgba(255,255,255,0.1);
        }

        /* 操作人员统计卡片 */
        .operator-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 1rem;
            overflow: hidden;
            transition: box-shadow 0.2s;
        }

        .operator-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .operator-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 1rem;
            font-weight: bold;
            cursor: pointer; /* 添加点击手势 */
            transition: background-color 0.2s;
        }

        .operator-header:hover {
            background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%);
        }

        .operator-projects {
            padding: 1rem;
            display: none; /* 默认隐藏项目明细 */
        }

        .operator-projects.show {
            display: block; /* 展开时显示 */
        }

        /* 折叠图标样式 */
        .operator-collapse-icon {
            transition: transform 0.2s;
            margin-left: 0.5rem;
        }

        .operator-collapse-icon.expanded {
            transform: rotate(90deg);
        }

        .project-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .project-item:last-child {
            border-bottom: none;
        }

        .project-name {
            font-weight: 500;
            color: #495057;
        }

        .project-meta {
            font-size: 0.85em;
            color: #6c757d;
        }

        .file-count-badge {
            background: #28a745;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        /* 移动端模态框优化 */
        @media (max-width: 767px) {
            .modal-content-custom {
                width: 95%;
                margin: 2% auto;
                max-height: 95vh;
            }

            .modal-header-custom {
                padding: 1rem;
            }

            .modal-header-custom h4 {
                font-size: 1.1rem;
                line-height: 1.3;
            }

            .modal-body-custom {
                padding: 1rem;
                max-height: 75vh;
            }

            .modal-footer-custom {
                padding: 0.75rem 1rem;
            }

            /* 操作人员卡片移动端优化 */
            .operator-header {
                padding: 0.75rem;
                flex-wrap: wrap;
            }

            .operator-header .d-flex {
                flex-wrap: wrap;
                gap: 0.5rem;
            }

            .operator-projects {
                padding: 0.75rem;
            }

            /* 项目条目移动端优化 */
            .project-item {
                flex-direction: column;
                align-items: flex-start;
                padding: 0.75rem 0;
                gap: 0.5rem;
            }

            .project-item > div:first-child {
                width: 100%;
            }

            .project-item > div:last-child {
                width: 100%;
                text-align: left;
            }

            /* 项目名称在模态框中的处理 */
            .project-name {
                font-size: 0.95rem;
                line-height: 1.3;
                word-break: break-word;
                overflow-wrap: break-word;
            }

            /* 项目元数据移动端优化 */
            .project-meta {
                font-size: 0.8rem;
                line-height: 1.4;
                margin-top: 0.25rem;
            }

            .project-meta .badge-custom {
                font-size: 0.7rem;
                padding: 0.15rem 0.3rem;
                margin-bottom: 0.25rem;
                display: inline-block;
            }

            .project-meta .text-muted {
                display: block;
                margin-top: 0.25rem;
                font-size: 0.75rem;
                word-break: break-word;
            }

            /* 文件计数徽章移动端优化 */
            .file-count-badge {
                font-size: 0.75rem;
                padding: 0.2rem 0.4rem;
            }

            /* 确保长文本不会溢出 */
            .operator-card {
                overflow: hidden;
            }

            .operator-card * {
                word-wrap: break-word;
                overflow-wrap: break-word;
            }
        }

        /* 极小屏幕进一步优化 */
        @media (max-width: 480px) {
            .modal-content-custom {
                width: 98%;
                margin: 1% auto;
                max-height: 98vh;
            }

            .modal-header-custom {
                padding: 0.75rem;
            }

            .modal-header-custom h4 {
                font-size: 1rem;
            }

            .modal-body-custom {
                padding: 0.75rem;
                max-height: 80vh;
            }

            .operator-header {
                padding: 0.5rem;
            }

            .operator-projects {
                padding: 0.5rem;
            }

            .project-item {
                padding: 0.5rem 0;
            }

            .project-name {
                font-size: 0.9rem;
            }

            .project-meta {
                font-size: 0.75rem;
            }

            .project-meta .badge-custom {
                font-size: 0.65rem;
                padding: 0.1rem 0.25rem;
            }

            .project-meta .text-muted {
                font-size: 0.7rem;
            }

            .file-count-badge {
                font-size: 0.7rem;
                padding: 0.15rem 0.3rem;
            }

            /* 关闭按钮优化 */
            .close-btn {
                font-size: 1.2rem;
                padding: 0.25rem;
            }

            /* 项目详情区域优化 */
            .project-details .text-muted {
                font-size: 0.7rem !important;
                margin-bottom: 0.1rem;
            }
        }

        /* 通用项目详情样式 */
        .project-details {
            margin-top: 0.25rem;
        }

        .project-details .text-muted {
            font-size: 0.8rem;
            line-height: 1.3;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="mb-0">
                <i class="bi bi-bar-chart-line me-2"></i>
                文件上传统计
            </h1>
            <p class="mb-0 mt-2">实时监控文件上传情况和项目进度</p>
        </div>
    </div>

    <div class="container">
        <!-- 筛选区域 -->
        <div class="filter-section">
            <h5 class="mb-3">
                <i class="bi bi-funnel me-2"></i>
                筛选条件
            </h5>
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">快速选择</label>
                    <select class="form-select" id="quickSelect">
                        <option value="7">最近7天</option>
                        <option value="30">最近30天</option>
                        <option value="90">最近90天</option>
                        <option value="custom">自定义日期</option>
                    </select>
                </div>
                <div class="col-md-3" id="startDateGroup" style="display: none;">
                    <label class="form-label">开始日期</label>
                    <input type="date" class="form-control" id="startDate">
                </div>
                <div class="col-md-3" id="endDateGroup" style="display: none;">
                    <label class="form-label">结束日期</label>
                    <input type="date" class="form-control" id="endDate">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button class="btn btn-primary w-100" id="searchBtn">
                        <i class="bi bi-search me-1"></i>
                        查询
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="row gx-3 gy-3 mb-4" id="summarySection" style="display: none;">
            <div class="col-xxl-2 col-xl-2 col-lg-4 col-md-6 col-sm-6">
                <div class="stats-card text-center h-100">
                    <h3 class="text-primary mb-1" id="totalFiles">-</h3>
                    <p class="mb-0">总文件数</p>
                </div>
            </div>
            <div class="col-xxl-2 col-xl-2 col-lg-4 col-md-6 col-sm-6">
                <div class="stats-card text-center h-100">
                    <h3 class="text-success mb-1" id="totalProjects">-</h3>
                    <p class="mb-0">项目数</p>
                </div>
            </div>
            <div class="col-xxl-2 col-xl-2 col-lg-4 col-md-6 col-sm-6">
                <div class="stats-card text-center h-100">
                    <h3 class="text-info mb-1" id="totalDays">-</h3>
                    <p class="mb-0">统计天数</p>
                </div>
            </div>
            <div class="col-xxl-2 col-xl-2 col-lg-4 col-md-6 col-sm-6">
                <div class="stats-card text-center h-100">
                    <h3 class="text-warning mb-1" id="avgFiles">-</h3>
                    <p class="mb-0">日均文件数</p>
                </div>
            </div>
            <div class="col-xxl-4 col-xl-4 col-lg-8 col-md-12 col-sm-12">
                <div class="stats-card text-center h-100">
                    <h3 class="text-secondary mb-1" id="dateRange">-</h3>
                    <p class="mb-0">统计范围</p>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading" id="loadingSection">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载数据...</p>
        </div>

        <!-- 无数据状态 -->
        <div class="no-data" id="noDataSection" style="display: none;">
            <i class="bi bi-inbox display-1 text-muted"></i>
            <h4 class="mt-3">暂无数据</h4>
            <p>所选时间范围内没有文件上传记录</p>
        </div>

        <!-- 每日统计详情 -->
        <div id="dailyStatsSection"></div>
    </div>

    <!-- 项目详情模态框 -->
    <div id="projectDetailModal" class="project-detail-modal">
        <div class="modal-content-custom">
            <div class="modal-header-custom">
                <h4 class="mb-0">
                    <i class="bi bi-people-fill me-2"></i>
                    <span id="modalTitle">操作人员项目统计</span>
                </h4>
                <button type="button" class="close-btn" onclick="closeProjectDetailModal()">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
            <div class="modal-body-custom" id="modalBody">
                <!-- 动态内容将在这里生成 -->
            </div>
        </div>
    </div>

    <script src="./bootstrap.bundle.min.js"></script>
    <script>
        const API_BASE_URL = 'http://127.0.0.1:5000';

        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认日期
            const today = new Date();
            const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

            document.getElementById('endDate').value = today.toISOString().split('T')[0];
            document.getElementById('startDate').value = sevenDaysAgo.toISOString().split('T')[0];

            // 绑定事件
            bindEvents();

            // 加载默认数据
            loadStats();
        });

        function bindEvents() {
            // 快速选择变化事件
            document.getElementById('quickSelect').addEventListener('change', function() {
                const value = this.value;
                const startDateGroup = document.getElementById('startDateGroup');
                const endDateGroup = document.getElementById('endDateGroup');
                
                if (value === 'custom') {
                    startDateGroup.style.display = 'block';
                    endDateGroup.style.display = 'block';
                } else {
                    startDateGroup.style.display = 'none';
                    endDateGroup.style.display = 'none';
                }
            });

            // 查询按钮事件
            document.getElementById('searchBtn').addEventListener('click', loadStats);
        }

        function loadStats() {
            showLoading();

            const quickSelect = document.getElementById('quickSelect').value;
            let url = `${API_BASE_URL}/file_stats`;

            // 构建查询参数
            const params = new URLSearchParams();

            if (quickSelect === 'custom') {
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;

                if (!startDate || !endDate) {
                    alert('请选择开始和结束日期');
                    hideLoading();
                    return;
                }

                params.append('start_date', startDate);
                params.append('end_date', endDate);
            } else {
                params.append('days', quickSelect);
            }

            // 添加权限key参数（如果存在）
            const accessKey = getUrlParameter('key');
            if (accessKey) {
                params.append('key', accessKey);
            }

            url += '?' + params.toString();

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        displayStats(data.data);
                    } else {
                        showError(data.msg || '加载数据失败');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showError('网络错误，请重试');
                    console.error('Error:', error);
                });
        }

        function displayStats(data) {
            const { summary, daily_stats, permission } = data;

            // 保存数据到全局变量，供模态框使用
            currentStatsData = data;

            // 显示权限信息（如果有key参数）
            if (permission && permission.has_key) {
                const accessKey = getUrlParameter('key');
                let permissionText = '';
                if (permission.is_admin) {
                    permissionText = '管理员权限 - 可查看所有地市数据';
                } else {
                    permissionText = `地市权限 - 仅显示 ${permission.allowed_cities.join(', ')} 的数据`;
                }

                // 在页面顶部显示权限提示
                const headerElement = document.querySelector('.header .container');
                const existingAlert = document.getElementById('permissionAlert');
                if (existingAlert) {
                    existingAlert.remove();
                }

                const alertDiv = document.createElement('div');
                alertDiv.id = 'permissionAlert';
                alertDiv.className = 'alert alert-info mt-3 mb-0';
                alertDiv.innerHTML = `
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>访问权限：</strong>${permissionText}
                `;
                headerElement.appendChild(alertDiv);
            }
            
            // 过滤每日统计数据，排除单日文件数少于2的项目
            const filteredDailyStats = daily_stats.map(dayData => {
                // 按项目分组
                const projectGroups = {};
                dayData.files.forEach(file => {
                    const projectKey = `${file.project.id}_${file.project.code}`;
                    if (!projectGroups[projectKey]) {
                        projectGroups[projectKey] = {
                            project: file.project,
                            files: []
                        };
                    }
                    projectGroups[projectKey].files.push(file);
                });

                // 过滤掉文件数少于2的项目
                const validProjectGroups = {};
                Object.keys(projectGroups).forEach(projectKey => {
                    if (projectGroups[projectKey].files.length >= 2) {
                        validProjectGroups[projectKey] = projectGroups[projectKey];
                    }
                });

                // 重新构建文件列表
                const filteredFiles = [];
                Object.values(validProjectGroups).forEach(group => {
                    filteredFiles.push(...group.files);
                });
                
                return {
                    ...dayData,
                    files: filteredFiles,
                    total_files: filteredFiles.length,
                    validProjectGroups: validProjectGroups // 保存有效项目组用于后续计算
                };
            });
            
            // 计算过滤后的总项目数（去重）- 修复逻辑
            const allValidProjects = new Set();
            let totalDisplayedProjects = 0; // 页面实际显示的项目数
            
            filteredDailyStats.forEach(day => {
                if (day.validProjectGroups) {
                    // 统计当天显示的项目数
                    const dayProjectCount = Object.keys(day.validProjectGroups).length;
                    totalDisplayedProjects += dayProjectCount;
                    
                    // 添加到去重集合中
                    Object.values(day.validProjectGroups).forEach(group => {
                        allValidProjects.add(group.project.id);
                    });
                }
            });

            // 计算过滤后的总文件数
            const totalFilteredFiles = filteredDailyStats.reduce((sum, day) => sum + day.total_files, 0);
            
            // 更新概览统计 - 使用去重后的项目数
            document.getElementById('totalFiles').textContent = totalFilteredFiles;
            document.getElementById('totalProjects').textContent = allValidProjects.size;
            document.getElementById('totalDays').textContent = summary.total_days;
            document.getElementById('avgFiles').textContent = summary.total_days > 0 ? 
                Math.round(totalFilteredFiles / summary.total_days * 10) / 10 : 0;
            document.getElementById('dateRange').textContent = `${summary.start_date} 至 ${summary.end_date}`;
            
            // 显示概览区域
            document.getElementById('summarySection').style.display = 'block';
            
            if (filteredDailyStats.length === 0 || totalFilteredFiles === 0) {
                showNoData();
                return;
            }
            
            // 渲染每日统计
            const dailyStatsSection = document.getElementById('dailyStatsSection');
            dailyStatsSection.innerHTML = '';
            
            filteredDailyStats.forEach(dayData => {
                // 只渲染有有效文件的日期
                if (dayData.total_files > 0) {
                    const dayCard = createDayCard(dayData);
                    dailyStatsSection.appendChild(dayCard);
                }
            });
            
            document.getElementById('dailyStatsSection').style.display = 'block';
        }

        function createDayCard(dayData) {
            const card = document.createElement('div');
            card.className = 'daily-card';
            
            // 使用已经过滤好的项目组数据
            const projectGroups = dayData.validProjectGroups || {};
            const projectCount = Object.keys(projectGroups).length;
            
            const header = document.createElement('div');
            header.className = 'daily-header';
            header.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <span>${dayData.date}</span>
                    <div>
                        <span class="badge bg-light text-dark me-2 clickable-badge"
                              onclick="showProjectDetail('${dayData.date}', 'daily')"
                              title="点击查看操作人员详情">${projectCount} 个项目</span>
                        <span class="badge bg-light text-dark">${dayData.total_files} 个文件</span>
                    </div>
                </div>
            `;
            
            const body = document.createElement('div');
            
            if (dayData.files.length === 0) {
                body.innerHTML = '<div class="text-center p-3 text-muted">当天无符合条件的文件上传</div>';
            } else {
                // 渲染每个项目组（所有项目都已经满足文件数>=2的条件）
                Object.values(projectGroups).forEach((group, index) => {
                    // 找到最新文件时间
                    const latestTime = group.files.reduce((latest, file) => {
                        return file.upload_time > latest ? file.upload_time : latest;
                    }, '00:00:00');

                    // 项目标题（可点击折叠）
                    const projectHeader = document.createElement('div');
                    projectHeader.className = 'project-header';
                    const projectId = `project_${dayData.date}_${index}`;
                    projectHeader.style.cssText = `
                        background-color: #f8f9fa;
                        padding: 0.75rem 1rem;
                        border-bottom: 2px solid #dee2e6;
                        font-weight: bold;
                        color: #495057;
                        cursor: pointer;
                    `;
                    projectHeader.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center flex-grow-1 min-width-0">
                                <i class="bi bi-chevron-right collapse-icon me-2 flex-shrink-0"></i>
                                <span class="badge badge-custom bg-primary me-2 flex-shrink-0">${group.project.city}</span>
                                <span class="badge badge-custom bg-success me-2 flex-shrink-0">${group.project.type}</span>
                                <span class="me-2 project-name-display flex-grow-1 min-width-0" title="${group.project.name}">${group.project.name}</span>
                                <span class="text-muted flex-shrink-0">工单: ${group.project.code}</span>
                            </div>
                            <div class="text-end flex-shrink-0 ms-3">
                                <span class="badge bg-secondary me-2 clickable-badge"
                                      onclick="showProjectDetail('${dayData.date}', 'project', '${group.project.id}')"
                                      title="点击查看该项目操作人员详情">${group.files.length} 个文件</span>
                                <div class="latest-file-time">最新: ${latestTime}</div>
                            </div>
                        </div>
                    `;

                    // 项目文件列表容器
                    const projectFiles = document.createElement('div');
                    projectFiles.className = 'project-files';
                    projectFiles.id = projectId;

                    // 项目下的文件列表
                    group.files.forEach(file => {
                        const fileItem = document.createElement('div');
                        fileItem.className = 'file-item';
                        fileItem.style.paddingLeft = '2rem';
                        fileItem.innerHTML = `
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="flex-grow-1">
                                    <i class="bi bi-file-earmark-text me-2 text-primary"></i>
                                    <span class="fw-bold">${file.file_name}</span>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">${file.upload_time}</small>
                                </div>
                            </div>
                        `;
                        projectFiles.appendChild(fileItem);
                    });

                    // 添加点击事件
                    projectHeader.addEventListener('click', function() {
                        const filesContainer = document.getElementById(projectId);
                        const icon = this.querySelector('.collapse-icon');
                        
                        if (filesContainer.classList.contains('show')) {
                            filesContainer.classList.remove('show');
                            icon.classList.remove('expanded');
                        } else {
                            filesContainer.classList.add('show');
                            icon.classList.add('expanded');
                        }
                    });

                    body.appendChild(projectHeader);
                    body.appendChild(projectFiles);
                });
            }
            
            card.appendChild(header);
            card.appendChild(body);
            
            return card;
        }

        function showLoading() {
            document.getElementById('loadingSection').style.display = 'block';
            document.getElementById('noDataSection').style.display = 'none';
            document.getElementById('dailyStatsSection').style.display = 'none';
            document.getElementById('summarySection').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loadingSection').style.display = 'none';
        }

        function showNoData() {
            document.getElementById('noDataSection').style.display = 'block';
            document.getElementById('dailyStatsSection').style.display = 'none';
        }

        function showError(message) {
            alert(message);
        }

        // 全局变量存储当前数据，用于模态框显示
        let currentStatsData = null;

        // 显示项目详情模态框
        function showProjectDetail(date, type, projectId = null) {
            if (!currentStatsData) {
                showError('数据未加载完成，请稍后再试');
                return;
            }

            const modal = document.getElementById('projectDetailModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalBody = document.getElementById('modalBody');

            // 根据类型设置标题
            if (type === 'daily') {
                modalTitle.textContent = `${date} - 操作人员项目统计`;
            } else if (type === 'project') {
                modalTitle.textContent = `${date} - 单个项目操作详情`;
            }

            // 生成模态框内容
            const content = generateModalContent(date, type, projectId);
            modalBody.innerHTML = content;

            // 显示模态框
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden'; // 防止背景滚动
        }

        // 关闭项目详情模态框
        function closeProjectDetailModal() {
            const modal = document.getElementById('projectDetailModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto'; // 恢复背景滚动
        }

        // 生成模态框内容
        function generateModalContent(date, type, projectId) {
            if (!currentStatsData || !currentStatsData.daily_stats) {
                return '<div class="text-center p-4"><p class="text-muted">暂无数据</p></div>';
            }

            // 找到指定日期的数据
            const dayData = currentStatsData.daily_stats.find(day => day.date === date);
            if (!dayData || !dayData.files || dayData.files.length === 0) {
                return '<div class="text-center p-4"><p class="text-muted">该日期暂无数据</p></div>';
            }

            let filesToProcess = dayData.files;

            // 如果是单个项目模式，过滤出该项目的文件
            if (type === 'project' && projectId) {
                filesToProcess = dayData.files.filter(file => file.project.id.toString() === projectId.toString());
                if (filesToProcess.length === 0) {
                    return '<div class="text-center p-4"><p class="text-muted">该项目暂无数据</p></div>';
                }
                // 检查单个项目的文件数是否满足条件
                if (filesToProcess.length < 2) {
                    return '<div class="text-center p-4"><p class="text-muted">该项目文件数少于2个，不符合显示条件</p></div>';
                }
            }

            // 按项目分组，应用文件数>=2的过滤规则
            const projectGroups = {};
            filesToProcess.forEach(file => {
                const projectKey = `${file.project.id}_${file.project.code}`;
                if (!projectGroups[projectKey]) {
                    projectGroups[projectKey] = {
                        project: file.project,
                        files: []
                    };
                }
                projectGroups[projectKey].files.push(file);
            });

            // 过滤掉文件数少于2的项目
            const validProjectGroups = {};
            Object.keys(projectGroups).forEach(projectKey => {
                if (projectGroups[projectKey].files.length >= 2) {
                    validProjectGroups[projectKey] = projectGroups[projectKey];
                }
            });

            // 如果过滤后没有有效项目，显示提示信息
            if (Object.keys(validProjectGroups).length === 0) {
                return '<div class="text-center p-4"><p class="text-muted">该日期没有符合条件的项目（文件数≥2）</p></div>';
            }

            // 重新构建过滤后的文件列表
            const filteredFiles = [];
            Object.values(validProjectGroups).forEach(group => {
                filteredFiles.push(...group.files);
            });

            // 按操作人员分组统计（使用过滤后的文件）
            const operatorStats = {};
            filteredFiles.forEach(file => {
                const operator = file.project.czry || '未知操作员';
                if (!operatorStats[operator]) {
                    operatorStats[operator] = {
                        name: operator,
                        projects: {},
                        totalFiles: 0
                    };
                }

                const projectKey = `${file.project.id}_${file.project.code}`;
                if (!operatorStats[operator].projects[projectKey]) {
                    operatorStats[operator].projects[projectKey] = {
                        project: file.project,
                        files: []
                    };
                }

                operatorStats[operator].projects[projectKey].files.push(file);
                operatorStats[operator].totalFiles++;
            });

            // 生成HTML内容
            let html = '';
            const operators = Object.values(operatorStats);

            if (operators.length === 0) {
                return '<div class="text-center p-4"><p class="text-muted">暂无操作人员数据</p></div>';
            }

            // 添加过滤说明
            const totalValidProjects = Object.keys(validProjectGroups).length;
            const totalValidFiles = filteredFiles.length;
            html += `
                <div class="alert alert-info mb-3">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>数据说明：</strong>仅显示文件数量≥2的项目，共 ${totalValidProjects} 个项目，${totalValidFiles} 个文件
                </div>
            `;

            // 按总文件数排序
            operators.sort((a, b) => b.totalFiles - a.totalFiles);

            operators.forEach((operator, index) => {
                const projectCount = Object.keys(operator.projects).length;
                const operatorId = `operator_${index}`;

                html += `
                    <div class="operator-card">
                        <div class="operator-header" onclick="toggleOperatorProjects('${operatorId}')">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>
                                    <i class="bi bi-person-fill me-2"></i>
                                    ${operator.name}
                                    <i class="bi bi-chevron-right operator-collapse-icon" id="icon_${operatorId}"></i>
                                </span>
                                <div>
                                    <span class="badge bg-light text-dark me-2">${projectCount} 个项目</span>
                                    <span class="badge bg-light text-dark">${operator.totalFiles} 个文件</span>
                                </div>
                            </div>
                        </div>
                        <div class="operator-projects" id="${operatorId}">
                `;

                // 按项目文件数排序
                const projects = Object.values(operator.projects);
                projects.sort((a, b) => b.files.length - a.files.length);

                projects.forEach(projectGroup => {
                    const project = projectGroup.project;
                    const fileCount = projectGroup.files.length;

                    // 计算最新文件时间
                    const latestTime = projectGroup.files.reduce((latest, file) => {
                        return file.upload_time > latest ? file.upload_time : latest;
                    }, '00:00:00');

                    html += `
                        <div class="project-item">
                            <div>
                                <div class="project-name" title="${project.name}">${project.name}</div>
                                <div class="project-meta">
                                    <div class="d-flex flex-wrap gap-1 mb-1">
                                        <span class="badge badge-custom bg-primary">${project.city}</span>
                                        <span class="badge badge-custom bg-success">${project.type}</span>
                                    </div>
                                    <div class="project-details">
                                        <span class="text-muted d-block">工单: ${project.code}</span>
                                        <span class="text-muted d-block">最新: ${latestTime}</span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <span class="file-count-badge">${fileCount} 个文件</span>
                            </div>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            });

            return html;
        }

        // 切换操作人员项目明细显示/隐藏
        function toggleOperatorProjects(operatorId) {
            const projectsContainer = document.getElementById(operatorId);
            const icon = document.getElementById(`icon_${operatorId}`);

            if (projectsContainer.classList.contains('show')) {
                projectsContainer.classList.remove('show');
                icon.classList.remove('expanded');
            } else {
                projectsContainer.classList.add('show');
                icon.classList.add('expanded');
            }
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('projectDetailModal');
            if (event.target === modal) {
                closeProjectDetailModal();
            }
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeProjectDetailModal();
            }
        });
    </script>
</body>
</html>














