from flask import Flask, request, jsonify, send_from_directory, make_response
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import (
    JWTManager,
    create_access_token,
    jwt_required,
    get_jwt_identity,
)
import os, re, shutil, time, random, io, uuid, sys
from datetime import timed<PERSON><PERSON>
from flask_cors import CORS
from PIL import Image, ImageDraw, ImageFont
from waitress import serve
import logging
from logging.handlers import TimedRotatingFileHandler
import functools
import ctypes
from ctypes import wintypes

# 配置日志系统
def setup_logging():
    """配置应用日志系统"""
    # 创建logs目录
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 配置日志格式
    log_format = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y%m%d %H:%M:%S'
    )

    # 创建应用日志记录器
    app_logger = logging.getLogger('app')
    app_logger.setLevel(logging.INFO)
    # 防止日志传播到根记录器，避免重复
    app_logger.propagate = False

    # 统一日志文件（按日轮转）
    file_handler = TimedRotatingFileHandler(
        os.path.join(log_dir, 'app.log'),
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    file_handler.setFormatter(log_format)
    file_handler.setLevel(logging.INFO)

    # 控制台输出
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(log_format)
    console_handler.setLevel(logging.INFO)

    # 添加处理器
    app_logger.addHandler(file_handler)
    app_logger.addHandler(console_handler)

    return app_logger

# Windows 控制台 QuickEdit 模式禁用函数
def disable_quickedit():
    """禁用 Windows 控制台的 QuickEdit 模式，防止点击控制台导致服务器暂停"""
    try:
        if sys.platform == "win32":
            # 获取控制台句柄
            kernel32 = ctypes.windll.kernel32
            handle = kernel32.GetStdHandle(-10)  # STD_INPUT_HANDLE

            # 获取当前控制台模式
            mode = wintypes.DWORD()
            kernel32.GetConsoleMode(handle, ctypes.byref(mode))

            # 禁用 QuickEdit 模式 (ENABLE_QUICK_EDIT_MODE = 0x0040)
            # 禁用 Insert 模式 (ENABLE_INSERT_MODE = 0x0020)
            new_mode = mode.value & ~(0x0040 | 0x0020)

            # 设置新的控制台模式
            result = kernel32.SetConsoleMode(handle, new_mode)

            if result:
                print("✓ 已禁用控制台 QuickEdit 模式，防止点击暂停服务器")
                return True
            else:
                print("✗ 禁用 QuickEdit 模式失败")
                return False
        else:
            # 非 Windows 系统不需要处理
            return True

    except Exception as e:
        print(f"✗ 禁用 QuickEdit 模式时出错: {e}")
        return False

# 初始化日志系统
logger = setup_logging()

# 禁用 Windows 控制台 QuickEdit 模式
disable_quickedit()

# 获取客户端IP地址的辅助函数
def get_client_ip():
    """获取客户端真实IP地址"""
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr

# 脱敏处理函数
def mask_sensitive_data(data, field_name):
    """对敏感数据进行脱敏处理
    if not data:
        return data

    if field_name in ['password', 'captcha']:
        return '***'
    elif field_name == 'phone':
        if len(str(data)) >= 7:
            return str(data)[:3] + '****' + str(data)[-4:]
        return '***'
    elif field_name == 'username':
        if len(str(data)) >= 3:
            return str(data)[:1] + '***' + str(data)[-1:]
        return '***'"""
    return data

# 日志记录装饰器
def log_api_call(operation_type):
    """API调用日志装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            client_ip = get_client_ip()
            user_identity = None

            # 尝试获取用户身份
            try:
                if hasattr(request, 'headers') and 'Authorization' in request.headers:
                    user_identity = get_jwt_identity()
            except:
                pass

            try:
                result = func(*args, **kwargs)
                return result

            except Exception as e:
                # 记录失败的操作（只记录未被函数内部处理的异常）
                logger.error(f"{operation_type}异常 - 用户: {mask_sensitive_data(user_identity, 'phone')} - IP: {client_ip} - 错误: {str(e)}")
                raise

        return wrapper
    return decorator

# 初始化Flask应用
logger.info("开始初始化应用")
app = Flask(__name__)

CORS(app, supports_credentials=True, expose_headers=["X-Captcha-ID"])
logger.info("应用初始化完成")

# 配置MySQL数据库连接
logger.info("开始配置数据库")
app.config["SQLALCHEMY_DATABASE_URI"] = (
    "mysql+pymysql://root:Aa1472356890@**************:3306/yekuo"
    #"mysql+pymysql://root:Aa1472356890@localhost:3306/yekuo"
)
app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False
# 优化数据库连接池配置
app.config["SQLALCHEMY_POOL_SIZE"] = 20  # 增加连接池大小
app.config["SQLALCHEMY_MAX_OVERFLOW"] = 30  # 增加溢出连接数
app.config["SQLALCHEMY_POOL_TIMEOUT"] = 30  # 连接超时时间
app.config["SQLALCHEMY_POOL_RECYCLE"] = 3600  # 连接回收时间(1小时)
app.config["SQLALCHEMY_POOL_PRE_PING"] = True  # 连接前ping检查
app.config["SQLALCHEMY_ENGINE_OPTIONS"] = {
    "pool_pre_ping": True,
    "pool_recycle": 3600,
    "connect_args": {
        "charset": "utf8mb4",
        "connect_timeout": 10,
        "read_timeout": 30,
        "write_timeout": 30,
        "autocommit": True,
    },
}

# 配置Session
app.config["SECRET_KEY"] = "fj38qrfj39jf39fj9fjq3fj"
app.config["SESSION_TYPE"] = "filesystem"
app.config["SESSION_COOKIE_HTTPONLY"] = True
app.config["SESSION_COOKIE_SAMESITE"] = "Lax"

# JWT配置
app.config["JWT_SECRET_KEY"] = "safsaf1454sa878"  # 生产环境应从环境变量获取
app.config["JWT_ACCESS_TOKEN_EXPIRES"] = timedelta(hours=8)  # 设置token过期时间

# 文件上传配置
app.config["ALLOWED_EXTENSIONS"] = {"pdf"}
app.config["MAX_CONTENT_LENGTH"] = 100 * 1024 * 1024  # 限制上传大小为100MB

Sql_City = [
    "城区",
    "环城",
    "汝南",
    "平舆",
    "新蔡",
    "确山",
    "泌阳",
    "正阳",
    "遂平",
    "西平",
    "上蔡",
]

# 地市权限控制配置
CITY_ACCESS_KEYS = {
    "cq2024": "城区",      # 城区访问key
    "hc2024": "环城",      # 环城访问key
    "rn2024": "汝南",      # 汝南访问key
    "py2024": "平舆",      # 平舆访问key
    "xc2024": "新蔡",      # 新蔡访问key
    "qs2024": "确山",      # 确山访问key
    "by2024": "泌阳",      # 泌阳访问key
    "zy2024": "正阳",      # 正阳访问key
    "sp2024": "遂平",      # 遂平访问key
    "xp2024": "西平",      # 西平访问key
    "sc2024": "上蔡",      # 上蔡访问key
    "admin2024": "ALL"     # 管理员key，可查看所有地市数据
}
#所有项目类型
Pro_Lx=[{'name':"高压用户",'num':17},
        {'name':"低压用户",'num':10},
        {'name':"光伏低压自然人",'num':18},
        {'name':"光伏低压非自然人",'num':20},
        {'name':"光伏高压",'num':20},
        {'name':"华宇高压用户",'num':17},
    ]

# 创建项目类型到数量的映射字典，提高查找效率
PRO_TYPE_NUM_MAP = {item['name']: item['num'] for item in Pro_Lx}
# print(PRO_TYPE_NUM_MAP)  # 生产环境应移除调试代码
# 初始化扩展
logger.info("初始化数据库连接")
try:
    db = SQLAlchemy(app)
    logger.info("数据库连接成功")
except Exception as e:
    logger.error(f"数据库连接失败: {str(e)}")
    raise

logger.info("初始化JWT管理器")
try:
    jwt = JWTManager(app)
    logger.info("JWT管理器初始化成功")
except Exception as e:
    logger.error(f"JWT管理器初始化失败: {str(e)}")
    raise

def get_executable_dir():
    """
    获取可执行文件所在目录的路径
    兼容开发环境和 PyInstaller 打包后的环境
    """
    if getattr(sys, 'frozen', False):
        # PyInstaller 打包后的环境
        # sys.executable 指向 .exe 文件的完整路径
        executable_dir = os.path.dirname(sys.executable)
    else:
        # 开发环境
        # __file__ 指向当前 Python 脚本文件
        executable_dir = os.path.dirname(os.path.abspath(__file__))

    return executable_dir

# 获取可执行文件目录并设置相对路径
BASE_DIR = get_executable_dir()
sys_temp = BASE_DIR
logger.info(f'运行目录{BASE_DIR}')
# 使用内存字典存储验证码
captcha_store = {}


# 生成验证码图片
def generate_captcha(width=120, height=40, chars_num=4):
    """生成验证码图片"""
    # 生成随机字符
    characters = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789"
    captcha_text = "".join(random.sample(characters, chars_num))

    # 创建图像
    image = Image.new("RGB", (width, height), color=(255, 255, 255))
    draw = ImageDraw.Draw(image)

    # 尝试使用系统字体
    try:
        font = ImageFont.truetype("arial.ttf", 28)
    except IOError:
        font = ImageFont.load_default()

    # 绘制字符
    for i, char in enumerate(captcha_text):
        draw.text(
            (15 + i * 25, random.randint(2, 10)),
            char,
            font=font,
            fill=(
                random.randint(0, 100),
                random.randint(0, 100),
                random.randint(0, 100),
            ),
        )

    # 添加干扰线
    for i in range(3):
        start_point = (random.randint(0, width // 3), random.randint(0, height))
        end_point = (random.randint(width // 3 * 2, width), random.randint(0, height))
        draw.line(
            [start_point, end_point],
            fill=(
                random.randint(100, 200),
                random.randint(100, 200),
                random.randint(100, 200),
            ),
            width=2,
        )

    # 添加干扰点
    for i in range(100):
        draw.point(
            (random.randint(0, width), random.randint(0, height)),
            fill=(
                random.randint(150, 250),
                random.randint(150, 250),
                random.randint(150, 250),
            ),
        )

    # 将图像转换为字节流
    image_bytes = io.BytesIO()
    image.save(image_bytes, format="JPEG")
    image_bytes.seek(0)

    return captcha_text, image_bytes


# 用户模型
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.Text(), unique=True, nullable=False)
    phone = db.Column(db.Text(), unique=True, nullable=False)
    city = db.Column(db.Text(), nullable=False)
    password = db.Column(db.Text(), nullable=False)
    date = db.Column(db.Text(), nullable=False, default=time.time())

    def to_dict(self):
        return {
            "id": self.id,
            "username": self.username,
            "phone": self.phone,
            "city": self.city,
            "password": self.password,
            "date": self.date,
            # 其他字段...
        }


class Baolist(db.Model):
    id = db.Column(db.Integer, primary_key=True, index=True)
    bao_name = db.Column(db.Text(), nullable=False)
    bao_state = db.Column(db.Boolean(), nullable=False)
    bao_lx = db.Column(db.Text(), nullable=False)
    bao_bm = db.Column(db.Text(), nullable=False)
    bao_city = db.Column(db.Text(), nullable=False)
    bao_start = db.Column(db.Text(), nullable=False, default=time.time())
    bao_end = db.Column(db.Text(), nullable=False)
    is_reject = db.Column(db.Boolean(), nullable=False)
    is_approve = db.Column(db.Boolean(), nullable=False)
    is_submit = db.Column(db.Boolean(), nullable=False)
    usercode = db.Column(db.Text(), nullable=False)
    gds = db.Column(db.Text(), nullable=False)
    czry = db.Column(db.Text(), nullable=False)

    # 新增字段
    gdlx = db.Column(db.Text(), nullable=True, comment='工单类型：增容、临时用电、新装')  # 工单类型
    sj_ywsl = db.Column(db.Text(), nullable=True, comment='业务受理时间')  # 业务受理时间
    sj_xckc = db.Column(db.Text(), nullable=True, comment='现场勘察时间')  # 现场勘察时间
    sj_fadf = db.Column(db.Text(), nullable=True, comment='方案答复时间')  # 方案答复时间
    sj_jgys = db.Column(db.Text(), nullable=True, comment='竣工验收时间')  # 竣工验收时间
    sj_zbsd = db.Column(db.Text(), nullable=True, comment='装表送电时间')  # 装表送电时间
    sj_gdsj = db.Column(db.Text(), nullable=True, comment='项目归档时间')  # 项目归档时间
    htrl = db.Column(db.Float(), nullable=True, comment='合同容量')  # 合同容量

    def to_dict(self):
        return {
            "id": self.id,
            "bao_name": self.bao_name,
            "bao_state": self.bao_state,
            "bao_lx": self.bao_lx,
            "bao_bm": self.bao_bm,
            "bao_city": self.bao_city,
            "bao_start": self.bao_start,
            "bao_end": self.bao_end,
            "is_reject": self.is_reject,
            "is_approve": self.is_approve,
            "is_submit": self.is_submit,
            "usercode": self.usercode,
            "gds": self.gds,
            "czry": self.czry,
            "gdlx": self.gdlx,
            "sj_ywsl": self.sj_ywsl,
            "sj_xckc": self.sj_xckc,
            "sj_fadf": self.sj_fadf,
            "sj_jgys": self.sj_jgys,
            "sj_zbsd": self.sj_zbsd,
            "sj_gdsj": self.sj_gdsj,
            "htrl": self.htrl,
            # 其他字段...
        }


class Filelist(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    file_name = db.Column(db.Text(), nullable=False)
    file_state = db.Column(db.Integer, nullable=False)
    file_lx = db.Column(db.Integer, nullable=False)
    file_bz = db.Column(db.Text(), nullable=False)
    bao_id = db.Column(db.Integer(), nullable=False)
    file_start = db.Column(db.Text(), nullable=False)
    file_end = db.Column(db.Text(), nullable=False)
    file_hash = db.Column(db.Text(), nullable=False)

    def to_dict(self):
        return {
            "id": self.id,
            "file_name": self.file_name,
            "file_state": self.file_state,
            "file_lx": self.file_lx,
            "file_bz": self.file_bz,
            "bao_id": self.bao_id,
            "file_start": self.file_start,
            "file_end": self.file_end,
            "file_hash": self.file_hash,
            # 其他字段...
        }


# 判断文件后缀是否合规
def allowed_file(filename):
    return (
        "." in filename
        and filename.rsplit(".", 1)[1].lower() in app.config["ALLOWED_EXTENSIONS"]
    )





# 验证码接口
@app.route("/captcha", methods=["GET"])
@log_api_call("获取验证码")
def get_captcha():
    """获取验证码接口"""
    captcha_text, image_bytes = generate_captcha()

    # 生成唯一ID
    captcha_id = str(uuid.uuid4())
    # 存储验证码到字典
    captcha_store[captcha_id] = captcha_text

    # 30秒后自动清除验证码
    def clear_captcha():
        import threading

        def delete_captcha():
            if captcha_id in captcha_store:
                del captcha_store[captcha_id]

        t = threading.Timer(30.0, delete_captcha)
        t.daemon = True
        t.start()

    clear_captcha()

    # 返回图片
    response = make_response(image_bytes.getvalue())
    response.headers["Content-Type"] = "image/jpeg"
    response.headers["X-Captcha-ID"] = captcha_id
    return response


# 用户登录接口
@app.route("/login", methods=["POST"])
@log_api_call("用户登录")
def login():
    data = request.get_json()
    username = data.get("username")
    password = data.get("password")
    captcha = data.get("captcha")
    captcha_id = request.headers.get("X-Captcha-ID")
    client_ip = get_client_ip()

    # 验证验证码
    if not captcha_id or captcha_id not in captcha_store:
        logger.warning(f"登录失败-验证码过期 - 用户: {mask_sensitive_data(username, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "验证码已过期，请刷新"}), 401

    stored_captcha = captcha_store.get(captcha_id)

    if not stored_captcha or stored_captcha.lower() != captcha.lower():
        logger.warning(f"登录失败-验证码错误 - 用户: {mask_sensitive_data(username, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "验证码错误"}), 401

    # 验证成功后删除验证码
    del captcha_store[captcha_id]

    user = User.query.filter_by(phone=username).first()

    if not user or user.password != password:
        logger.warning(f"登录失败-用户名或密码错误 - 用户: {mask_sensitive_data(username, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "用户名或密码错误"}), 401

    access_token = create_access_token(identity=username)

    # 检查是否需要强制修改密码
    need_password_change = user.password == "123456"

    # 记录成功登录
    logger.info(f"登录成功 - 用户: {mask_sensitive_data(user.username, 'username')} - "
               f"城市: {user.city} - IP: {client_ip}")

    response_data = {
        "access_token": access_token,
        "username": user.username,
        "city": user.city,
        "need_password_change": need_password_change
    }

    return jsonify(response_data), 200


# 密码修改接口（需要鉴权）
@app.route("/change_password", methods=["POST"])
@jwt_required()
@log_api_call("修改密码")
def change_password():
    phone = get_jwt_identity()
    data = request.get_json()
    client_ip = get_client_ip()

    current_password = data.get("current_password")
    new_password = data.get("new_password")
    confirm_password = data.get("confirm_password")

    # 参数验证
    if not current_password or not new_password or not confirm_password:
        logger.warning(f"修改密码失败-参数不完整 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "所有字段都是必填的"}), 400

    # 验证新密码确认
    if new_password != confirm_password:
        logger.warning(f"修改密码失败-密码不一致 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "新密码和确认密码不一致"}), 400

    # 验证新密码不能是默认密码
    if new_password == "123456":
        logger.warning(f"修改密码失败-使用默认密码 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "新密码不能是默认密码123456"}), 400

    # 验证新密码强度
    if len(new_password) < 6:
        logger.warning(f"修改密码失败-密码过短 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "密码长度至少6位"}), 400

    # 检查密码是否包含字母和数字
    has_letter = any(c.isalpha() for c in new_password)
    has_digit = any(c.isdigit() for c in new_password)
    if not (has_letter and has_digit):
        logger.warning(f"修改密码失败-密码复杂度不够 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "密码必须包含字母和数字"}), 400

    # 查询用户
    user = User.query.filter_by(phone=phone).first()
    if not user:
        logger.error(f"修改密码失败-用户不存在 - 手机: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "用户不存在"}), 401

    # 验证当前密码
    if user.password != current_password:
        logger.warning(f"修改密码失败-当前密码错误 - 用户: {mask_sensitive_data(user.username, 'username')} - IP: {client_ip}")
        return jsonify({"msg": "当前密码错误"}), 400

    # 验证新密码不能与当前密码相同
    if new_password == current_password:
        logger.warning(f"修改密码失败-新旧密码相同 - 用户: {mask_sensitive_data(user.username, 'username')} - IP: {client_ip}")
        return jsonify({"msg": "新密码不能与当前密码相同"}), 400

    # 更新密码
    user.password = new_password

    try:
        db.session.commit()
        # 记录密码修改成功日志
        logger.info(f"修改密码成功 - 用户: {mask_sensitive_data(user.username, 'username')} - IP: {client_ip}")
        return jsonify({"msg": "密码修改成功"}), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"修改密码失败-数据库错误 - 用户: {mask_sensitive_data(user.username, 'username')} - IP: {client_ip} - 错误: {str(e)}")
        return jsonify({"msg": f"密码修改失败: {str(e)}"}), 500


# PDF上传接口（需要鉴权）
@app.route("/upload", methods=["POST"])
@jwt_required()
@log_api_call("文件上传")
def upload_file():
    phone = get_jwt_identity()
    bao_id = request.values.get("bao_id")
    file_type = request.values.get("file_type")
    client_ip = get_client_ip()

    if bao_id == None or bao_id.isdigit() == False:
        logger.warning(f"文件上传失败-项目ID无效 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "bao_id is not"}), 400
    if file_type == None or file_type.isdigit() == False:
        logger.warning(f"文件上传失败-文件类型无效 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "file_type is not"}), 400

    bao_id = int(bao_id)
    file_type = int(file_type)

    baolist = Baolist.query.filter_by(id=bao_id).first()
    if not baolist:
        logger.warning(f"文件上传失败-项目不存在 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "项目不存在"}), 400

    mb_max = PRO_TYPE_NUM_MAP.get(baolist.bao_lx)
    if mb_max is None:
        logger.error(f"文件上传失败-未知项目类型 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip} - 项目类型: {baolist.bao_lx}")
        return jsonify({"msg": f"未知的项目类型: {baolist.bao_lx}"}), 400

    if file_type < 1 or file_type > mb_max:
        logger.warning(f"文件上传失败-文件类型超出范围 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "file_type is error max:" + str(mb_max)}), 400

    user = User.query.filter_by(phone=phone).first()
    if not user:
        logger.error(f"文件上传失败-用户不存在 - 手机: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "用户不存在"}), 401

    city = user.city

    if city != "VIP":
        if baolist.bao_city != city:
            logger.warning(f"文件上传失败-权限不足 - 用户: {mask_sensitive_data(user.username, 'username')} - IP: {client_ip}")
            return jsonify({"msg": "User exceeding authority"}), 400

    if "file" not in request.files:
        logger.warning(f"文件上传失败-未选择文件 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "No file part"}), 400

    file = request.files["file"]

    # 获取文件Hash
    # file_hash = hashlib.md5()
    # file_hash.update(file.read())
    # file.seek(0)  # 重置文件指针
    # file_md5_hash = file_hash.hexdigest()

    if file.filename == "":
        logger.warning(f"文件上传失败-文件名为空 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "No selected file"}), 400

    l_file = Filelist.query.filter(
        Filelist.bao_id == bao_id,
        Filelist.file_lx == file_type,
        # Filelist.file_hash == file_md5_hash,
    ).first()
    #判断files如果查到传过就判断是不是审核中或者已通过
    if l_file:
        if l_file.file_state == 0:
            logger.warning(f"文件上传失败-文件审核中 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
            return jsonify({"msg": "文件已在审核中,无法再次上传"}), 400
        if l_file.file_state == 1:
            logger.warning(f"文件上传失败-文件已通过 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
            return jsonify({"msg": "文件已通过,无法再次上传"}), 400
    if file and allowed_file(file.filename):

        pattern = re.compile(r"[^\u4e00-\u9fa5a-zA-Z0-9]")
        lin_file_name = pattern.sub(
            "", file.filename if file.filename is not None else ""
        )
        # 安全处理文件名，避免索引越界
        if len(lin_file_name) > 4:  # 确保文件名长度足够
            base_name = lin_file_name[0:-3]  # 移除.pdf扩展名
        else:
            return jsonify({"msg": "No file name"}), 400  # 如果文件名太短，使用默认名称

        filename = (
            time.strftime("%Y%m%d%H%M%S", time.localtime(time.time()))
            + "_"
            + base_name
            + ".pdf"
        )

        # 使用相对路径构建上传目录
        upload_subdir = os.path.join(
            "uploads",
            baolist.bao_city,
            baolist.bao_lx,
            time.strftime("%Y%m%d%H%M%S", time.localtime(int(baolist.bao_start)))
            + "_"
            + baolist.bao_name
            + "_"
            + baolist.bao_bm
        )
        path = os.path.join(BASE_DIR, upload_subdir)
        os.makedirs(path, exist_ok=True)
        file.save(os.path.join(path, filename))

        if l_file:
            old_file_path = os.path.join(path, l_file.file_name)
            if os.path.exists(old_file_path):
                os.remove(old_file_path)
            l_file.file_name = filename
            l_file.file_state = 0  # 0表示待审核
            l_file.file_lx = file_type
            l_file.bao_id = bao_id
            l_file.file_start = int(time.time())
            l_file.file_hash = ""
            l_file.file_bz = ""
        else:
            file_str = Filelist(
                file_name=filename,
                file_state=0,  # 0表示待审核
                file_lx=file_type,
                bao_id=bao_id,
                file_start=int(time.time()),
                file_hash="",
                file_bz="",
                file_end=""
            )
            db.session.add(file_str)

        try:
            db.session.commit()
            # 记录文件上传成功
            logger.info(f"文件上传成功 - 用户: {mask_sensitive_data(user.username, 'username')} - "
                       f"项目: {baolist.bao_name} - 文件: {filename} - IP: {client_ip}")
        except Exception as e:
            db.session.rollback()
            logger.error(f"文件上传失败-数据库错误 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip} - 错误: {str(e)}")
            return jsonify({"msg": f"数据库操作失败: {str(e)}"}), 500

        # 查询是否全部提交 如果全部提交就修改状态 把is_submit改成false
        bao_mb_num = PRO_TYPE_NUM_MAP.get(baolist.bao_lx)
        if bao_mb_num is None:
            return jsonify({"msg": f"未知的项目类型: {baolist.bao_lx}"}), 400
        filelist = Filelist.query.filter_by(bao_id=bao_id).all()
        my_is_reject = False
        my_is_approve = False
        if len(filelist) >= bao_mb_num:
            baolist.is_submit = False
            try:
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                return jsonify({"msg": f"数据库操作失败: {str(e)}"}), 500
        for item in filelist:
            if (
                item.file_state == 2
            ):  # 判断有没有文件是退回状态的 如果有就不清除包的退回状态
                my_is_reject = True
            if (
                item.file_state == 0
            ):  # 判断有没有文件是退回状态的 如果有就不清除包的退回状态
                my_is_approve = True

        # 如果没有退回就清除包的退回状态
        if my_is_reject == False:
            baolist.is_reject = False
        else:
            baolist.is_reject = True
            baolist.bao_state=False
        if my_is_approve == False:
            baolist.is_approve = False
        else:
            baolist.is_approve = True
            baolist.bao_state=False

        baolist.czry = user.username
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            return jsonify({"msg": f"数据库操作失败: {str(e)}"}), 500
        return jsonify({"msg": "File uploaded successfully", "filename": filename}), 200

    return jsonify({"msg": "File type not allowed"}), 400


# PDF下载接口（需要鉴权）
@app.route("/download", methods=["GET"])
@jwt_required()
@log_api_call("文件下载")
def download_file():
    phone = get_jwt_identity()
    client_ip = get_client_ip()

    # 查询文件信息
    bao_id = request.args.get("bao_id")
    file_lx = request.args.get("file_lx")

    if (
        bao_id == None
        or bao_id.isdigit() == False
        or file_lx == None
        or file_lx.isdigit() == False
    ):
        logger.warning(f"文件下载失败-参数无效 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "bao_id is not"}), 400

    bao_id = int(bao_id)
    file_lx = int(file_lx)

    # 查询用户信息
    user = User.query.filter_by(phone=phone).first()
    if not user:
        logger.error(f"文件下载失败-用户不存在 - 手机: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "Bad username or password"}), 401

    city = user.city
    baolist = Baolist.query.filter_by(id=bao_id).first()
    if not baolist:
        logger.warning(f"文件下载失败-项目不存在 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "项目不存在"}), 400

    if city != "VIP":
        if baolist.bao_city != city:
            logger.warning(f"文件下载失败-权限不足 - 用户: {mask_sensitive_data(user.username, 'username')} - IP: {client_ip}")
            return jsonify({"msg": "User exceeding authority"}), 400

    filelist = Filelist.query.filter(
        Filelist.bao_id == bao_id, Filelist.file_lx == file_lx
    ).first()

    if not filelist:
        logger.warning(f"文件下载失败-文件不存在 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "file not found"}), 400

    # 使用相对路径构建下载目录
    upload_subdir = os.path.join(
        "uploads",
        baolist.bao_city,
        baolist.bao_lx,
        time.strftime("%Y%m%d%H%M%S", time.localtime(int(baolist.bao_start)))
        + "_"
        + baolist.bao_name
        + "_"
        + baolist.bao_bm
    )
    directory = os.path.join(sys_temp, upload_subdir)

    # 记录文件下载成功
    logger.info(f"文件下载成功 - 用户: {mask_sensitive_data(user.username, 'username')} - "
               f"项目: {baolist.bao_name} - 文件: {filelist.file_name} - IP: {client_ip}")

    response = send_from_directory(
        directory,
        filelist.file_name,
        as_attachment=False,
        mimetype="application/octet-stream",
    )
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    response.headers["Content-Type"] = "application/pdf"
    return response


# 获取已上传项目列表_无分页（需要鉴权）
@app.route("/baos", methods=["GET"])
@jwt_required()
@log_api_call("查询项目列表")
def list_baos():
    """获取项目列表接口"""
    phone = get_jwt_identity()  # 获取用户手机号
    client_ip = get_client_ip()

    user = User.query.filter_by(phone=phone).first()
    city = user.city
    if not user or city == "":
        logger.error(f"查询项目失败-用户或城市无效 - 手机: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "Bad username or city"}), 400

    # 根据用户权限查询项目列表
    if city == "VIP":
        baolist = Baolist.query.all()  #'分页查询'
        my_city = Sql_City
        logger.info(f"查询项目列表-VIP用户 - 用户: {mask_sensitive_data(user.username, 'username')} - IP: {client_ip}")
    else:
        baolist = Baolist.query.filter_by(bao_city=city).all()
        my_city = [city]
        logger.info(f"查询项目列表-普通用户 - 用户: {mask_sensitive_data(user.username, 'username')} - 城市: {city} - IP: {client_ip}")

    # 如果没有项目，直接返回空结果
    if not baolist:
        ret_cont = {}

        for City_item in my_city:
            # 动态生成项目类型字典结构
            ret_cont[City_item] = {item['name']: [] for item in Pro_Lx}
        return jsonify(
            {
                "total": 0,
                "cont": ret_cont,
            }
        )

    # 获取所有项目的ID列表
    bao_ids = [bao.id for bao in baolist]

    # 一次性查询所有相关的文件列表，避免N+1查询
    all_files = Filelist.query.filter(Filelist.bao_id.in_(bao_ids)).all()

    # 将文件按bao_id分组，提高查找效率
    files_by_bao_id = {}
    for file in all_files:
        if file.bao_id not in files_by_bao_id:
            files_by_bao_id[file.bao_id] = []
        files_by_bao_id[file.bao_id].append(file.to_dict())

    # 转换项目数据并添加文件信息
    cont = []
    for item in baolist:
        item_dict = item.to_dict()
        # 获取该项目的文件列表，如果没有文件则为空列表
        item_dict["files"] = files_by_bao_id.get(item.id, [])
        cont.append(item_dict)

    # 按城市和类型组织数据结构
    ret_cont = {}
    for City_item in my_city:
        # 动态生成项目类型字典结构
        ret_cont[City_item] = {item['name']: [] for item in Pro_Lx}

        # 只遍历属于当前城市的项目
        for Cont_item in cont:
            if City_item == Cont_item["bao_city"]:
                bao_lx = Cont_item["bao_lx"]
                ret_cont[City_item][bao_lx].append(Cont_item)

    return jsonify(
        {
            "total": len(baolist),
            "cont": ret_cont,
        }
    )


# 新增项目接口（需要鉴权）
@app.route("/addbao", methods=["POST"])
@jwt_required()
@log_api_call("创建项目")
def add_bao():
    phone = get_jwt_identity()  # 获取用户手机号
    data = request.get_json()
    client_ip = get_client_ip()

    users = User.query.filter_by(phone=phone).first()
    if not users:
        logger.error(f"创建项目失败-用户不存在 - 手机: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "Bad username or password"}), 401
    city = users.city
    if city == "VIP":
        bao_city = data.get("bao_city")
    else:
        bao_city = city
    if not bao_city in Sql_City:
        return jsonify({"msg": f"{bao_city}地区选择错误"}), 400
    bao_name = data.get("bao_name")
    bao_lx = data.get("bao_lx")
    if bao_lx not in PRO_TYPE_NUM_MAP:
        return jsonify({"msg": f"{bao_lx}项目类型错误"}), 400
    bao_bm = data.get("bao_bm")
    if bao_bm.isdigit() == False:
        return jsonify({"msg": f"{bao_bm}项目工单编号错误"}), 400
    bao_start = int(time.time())
    usercode = data.get("usercode")
    gds = data.get("gds")

    # 新增字段
    htrl = data.get("htrl")  # 合同容量
    gdlx = data.get("gdlx")  # 工单类型

    # 验证usercode字段
    if not usercode or not isinstance(usercode, str):
        return jsonify({"msg": "户号不能为空"}), 400

    # 验证gds字段
    if not gds or not isinstance(gds, str):
        return jsonify({"msg": "供电所不能为空"}), 400

    # 验证合同容量字段
    if htrl is not None:
        try:
            htrl = float(htrl)
            if htrl < 0:
                return jsonify({"msg": "合同容量不能为负数"}), 400
        except (ValueError, TypeError):
            return jsonify({"msg": "合同容量必须是有效数字"}), 400

    # 验证工单类型字段
    valid_gdlx = ["增容", "临时用电", "新装"]
    if gdlx and gdlx not in valid_gdlx:
        return jsonify({"msg": f"工单类型错误，必须是：{', '.join(valid_gdlx)}"}), 400

    if (
        not bao_name
        or not bao_lx
        or not bao_bm
        or not bao_city
        or not usercode
        or not gds
    ):
        return jsonify({"msg": "参数不完整"}), 400
    m_bao = Baolist.query.filter_by(bao_bm=bao_bm).first()
    if m_bao:
        return jsonify({"msg": "工单编号已存在"}), 400
    bao_str = Baolist(
        bao_name=bao_name,
        bao_state=False,
        bao_lx=bao_lx,
        bao_bm=bao_bm,
        bao_city=bao_city,
        bao_start=bao_start,
        bao_end="",
        is_reject=False,
        is_approve=False,
        is_submit=True,
        usercode=usercode,
        gds=gds,
        czry=users.username,
        # 新增字段
        htrl=htrl,
        gdlx=gdlx,
        # 时间字段初始化为空，后续在工单流程中填写
        sj_ywsl=None,
        sj_xckc=None,
        sj_fadf=None,
        sj_jgys=None,
        sj_zbsd=None,
        sj_gdsj=None,
    )

    db.session.add(bao_str)
    try:
        db.session.commit()
        # 记录项目创建成功
        logger.info(f"创建项目成功 - 用户: {mask_sensitive_data(users.username, 'username')} - "
                   f"项目: {bao_name} - 编号: {bao_bm} - IP: {client_ip}")
    except Exception as e:
        db.session.rollback()
        logger.error(f"创建项目失败-数据库错误 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip} - 错误: {str(e)}")
        return jsonify({"msg": f"数据库操作失败: {str(e)}"}), 500

    b_time = bao_bm[0:8]
    maxtimes = 20240601
    if b_time[0:1] == "4":
        b_time = b_time[2:]
        maxtimes = 240601
    if bao_lx == "高压用户":
        if int(b_time) < maxtimes:
            file_str = Filelist(
                file_name="2024年6月1日前的项目此项无需收资",
                file_state=1,
                file_lx=4,
                file_start=int(time.time()),
                bao_id=bao_str.id,
                file_bz="",
                file_end=int(time.time()),
                file_hash=""
            )
            db.session.add(file_str)
            try:
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                return jsonify({"msg": f"数据库操作失败: {str(e)}"}), 500
    return jsonify({"msg": "新增成功", "bao_id": bao_str.id}), 200


# 删除项目接口（需要鉴权）
@app.route("/delbao", methods=["GET"])
@jwt_required()
@log_api_call("删除项目")
def del_bao():
    """删除项目接口"""
    phone = get_jwt_identity()  # 获取用户手机号
    client_ip = get_client_ip()

    user = User.query.filter_by(phone=phone).first()
    if not user:
        logger.error(f"删除项目失败-用户不存在 - 手机: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "Bad username or password"}), 401

    bao_id = request.args.get("bao_id", type=int)
    if not bao_id:
        logger.warning(f"删除项目失败-参数缺失 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "参数不完整"}), 400

    bao_str = Baolist.query.filter_by(id=bao_id).first()
    if not bao_str:
        logger.warning(f"删除项目失败-项目不存在 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "项目不存在"}), 400

    city = user.city
    if city != bao_str.bao_city and city != "VIP":
        logger.warning(f"删除项目失败-权限不足 - 用户: {mask_sensitive_data(user.username, 'username')} - IP: {client_ip}")
        return jsonify({"msg": "User exceeding authority"}), 400
    
    try:
        # 批量删除文件记录（更高效）
        deleted_files_count = Filelist.query.filter_by(bao_id=bao_id).delete()

        # 删除项目记录
        db.session.delete(bao_str)

        # 统一提交事务，保证原子性
        db.session.commit()

        logger.warning(f"删除项目成功 - 用户: {mask_sensitive_data(user.username, 'username')} - "
                      f"项目: {bao_str.bao_name} - 删除文件数: {deleted_files_count} - IP: {client_ip}")

    except Exception as e:
        db.session.rollback()
        logger.error(f"删除项目失败-数据库错误 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip} - 错误: {str(e)}")
        return jsonify({"msg": f"删除失败: {str(e)}"}), 500
    # 清理文件系统中的相关文件
    try:
        upload_subdir = os.path.join(
            "uploads",
            bao_str.bao_city,
            bao_str.bao_lx,
            time.strftime("%Y%m%d%H%M%S", time.localtime(int(bao_str.bao_start)))
            + "_"
            + bao_str.bao_name
            + "_"
            + bao_str.bao_bm
        )
        path = os.path.join(sys_temp, upload_subdir)
        if os.path.exists(path):
            shutil.rmtree(path)
            logger.info(f"删除项目文件目录成功")
        else:
            logger.info(f"项目文件目录不存在，跳过删除")
    except Exception as e:
        # 文件删除失败不影响数据库删除结果，只记录警告
        logger.warning(f"删除项目文件目录失败 - 错误: {str(e)}")

    return jsonify({"msg": "删除成功"}), 200


# file_reject接口（需要鉴权）


@app.route("/file_pending", methods=["POST"])
@jwt_required()
@log_api_call("文件审核")
def file_shenhe():
    """文件审核接口"""
    phone = get_jwt_identity()
    client_ip = get_client_ip()

    user = User.query.filter_by(phone=phone).first()
    if not user:
        logger.error(f"文件审核失败-用户不存在 - 手机: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "Bad username or password"}), 401
    city = user.city

    data = request.get_json()
    file_lx = data.get("file_lx")
    bao_id = data.get("bao_id")
    state = data.get("state")
    reason = data.get("reason")


    if not file_lx or not bao_id or not state:
        logger.warning(f"文件审核失败-参数不完整 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "参数不完整"}), 400

    if (
        not state in [0, 1, 2]
        or str(file_lx).isdigit() == False
        or str(bao_id).isdigit() == False
    ):
        logger.warning(f"文件审核失败-参数无效 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "文件状态错误0-2"}), 400

    file_lx = int(file_lx)
    bao_id = int(bao_id)
    state = int(state)

    if state == 2 and not reason:
        logger.warning(f"文件审核失败-缺少退回原因 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "退回原因不能为空"}), 400
    
    my_bao = Baolist.query.filter_by(id=bao_id).first()
    if not my_bao:
        logger.warning(f"文件审核失败-项目不存在 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "没有此项目"}), 400

    if city != "VIP" and city != my_bao.bao_city:
        logger.warning(f"文件审核失败-权限不足 - 用户: {mask_sensitive_data(user.username, 'username')} - IP: {client_ip}")
        return jsonify({"msg": "用户越权"}), 400

    # 使用 filter_by 确保只获取一条记录
    file_str = Filelist.query.filter_by(
        file_lx=file_lx,
        bao_id=bao_id,
    ).first()

    if not file_str:
        logger.warning(f"文件审核失败-文件不存在 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip}")
        return jsonify({"msg": "文件不存在"}), 400

    # 记录审核操作详情
    state_names = {0: "待审核", 1: "通过", 2: "退回"}
    state_name = state_names.get(state, "未知")

    if state == 0:
        file_str.file_state = state
        file_str.file_end = ""
        file_str.file_bz = ""
        my_bao.is_approve = True
    elif state == 1:
        file_str.file_state = state
        file_str.file_end = int(time.time())
        file_str.file_bz = ""
    elif state == 2:
        file_str.file_state = state
        file_str.file_bz = reason
        file_str.file_end = ""
        my_bao.is_reject = True

    try:
        db.session.commit()
        logger.info(f"文件审核成功 - 用户: {mask_sensitive_data(user.username, 'username')} - "
                   f"文件: {file_str.file_name} - 状态: {state_name} - IP: {client_ip}")
    except Exception as e:
        db.session.rollback()
        logger.error(f"文件审核失败-数据库错误 - 用户: {mask_sensitive_data(phone, 'phone')} - IP: {client_ip} - 错误: {str(e)}")
        return jsonify({"msg": f"数据库操作失败: {str(e)}"}), 500

    bao_lx = my_bao.bao_lx
    my_file = Filelist.query.filter_by(bao_id=bao_id).all()
    my_is_reject = False
    my_is_approve = False
    bao_mb_num = PRO_TYPE_NUM_MAP.get(bao_lx)
    if bao_mb_num is None:
        return jsonify({"msg": f"未知的项目类型: {bao_lx}"}), 400
    if len(my_file) >= bao_mb_num:
        for file in my_file:  # 查找所有文件是否有退回的
            if file.file_state == 2:
                my_is_reject = True
                break

        for file in my_file:  # 查找所有文件是否有待审核的
            if file.file_state == 0:
                my_is_approve = True
                break

         # 如果不存在退回或者待审核的文件，对应状态去掉
        if my_is_approve == False:
            my_bao.is_approve = False
        else:
            my_bao.is_approve = True
        if my_is_reject == False:
            my_bao.is_reject = False
        else:
            my_bao.is_reject = True
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            return jsonify({"msg": f"数据库操作失败: {str(e)}"}), 500
        if (
            my_is_approve == True or my_is_reject == True
        ):  # 如果存在退回或者待审核的文件，表示不能结束流程
            return jsonify({"msg": "操作成功"}), 200

        my_bao.bao_state = True
        my_bao.bao_end = int(time.time())
        my_bao.is_submit = False
        my_bao.is_approve = False
        my_bao.is_reject = False
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            return jsonify({"msg": f"数据库操作失败: {str(e)}"}), 500
        return jsonify({"msg": "审核完毕"}), 200
    return jsonify({"msg": "操作成功"}), 200


# 文件上传统计接口（无需鉴权）
@app.route("/file_stats", methods=["GET"])
@log_api_call("文件统计")
def file_upload_stats():
    """文件上传统计接口"""
    client_ip = get_client_ip()

    # 获取查询参数
    days = request.args.get("days", default=7, type=int)  # 默认最近7天
    start_date = request.args.get("start_date")  # 自定义开始日期 YYYY-MM-DD
    end_date = request.args.get("end_date")  # 自定义结束日期 YYYY-MM-DD
    access_key = request.args.get("key")  # 权限控制key

    # 权限控制：根据key参数过滤数据
    allowed_cities = None
    if access_key:
        if access_key in CITY_ACCESS_KEYS:
            city_permission = CITY_ACCESS_KEYS[access_key]
            if city_permission == "ALL":
                # 管理员key，可查看所有地市
                allowed_cities = Sql_City
                logger.info(f"文件统计-管理员访问 - Key: {access_key} - IP: {client_ip}")
            else:
                # 特定地市key，只能查看对应地市数据
                allowed_cities = [city_permission]
                logger.info(f"文件统计-地市访问 - Key: {access_key} - 城市: {city_permission} - IP: {client_ip}")
        else:
            logger.warning(f"文件统计-无效key - Key: {access_key} - IP: {client_ip}")
            return jsonify({"success": False, "msg": "无效的访问权限"}), 403
    else:
        # 无key参数，显示所有数据（保持向后兼容）
        allowed_cities = Sql_City
        logger.info(f"文件统计-无key访问 - IP: {client_ip}")

    try:
        # 计算时间范围
        if start_date and end_date:
            # 使用自定义日期范围
            import datetime

            start_timestamp = int(
                datetime.datetime.strptime(start_date, "%Y-%m-%d").timestamp()
            )
            end_timestamp = int(
                datetime.datetime.strptime(
                    end_date + " 23:59:59", "%Y-%m-%d %H:%M:%S"
                ).timestamp()
            )
        else:
            # 使用天数计算
            current_time = int(time.time())
            start_timestamp = current_time - (days * 24 * 60 * 60)
            end_timestamp = current_time

        # 查询指定时间范围内的文件及其对应的项目信息（使用JOIN避免N+1查询）
        query = (
            db.session.query(Filelist, Baolist)
            .join(Baolist, Filelist.bao_id == Baolist.id)
            .filter(
                Filelist.file_start >= start_timestamp,
                Filelist.file_start <= end_timestamp,
            )
        )

        # 根据权限过滤城市数据
        if allowed_cities and len(allowed_cities) < len(Sql_City):
            # 如果不是查看所有城市，则添加城市过滤条件
            query = query.filter(Baolist.bao_city.in_(allowed_cities))

        files_with_projects = query.all()

        # 按日期分组统计
        daily_stats = {}

        for file, project in files_with_projects:
            # 将时间戳转换为日期
            file_date = time.strftime("%Y-%m-%d", time.localtime(int(file.file_start)))

            if file_date not in daily_stats:
                daily_stats[file_date] = {
                    "date": file_date,
                    "total_files": 0,
                    "files": [],
                }

            file_info = {
                "file_id": file.id,
                "file_name": file.file_name,
                "file_type": file.file_lx,
                "upload_time": time.strftime(
                    "%H:%M:%S", time.localtime(int(file.file_start))
                ),
                "project": {
                    "id": project.id,
                    "name": project.bao_name,
                    "code": project.bao_bm,
                    "type": project.bao_lx,
                    "city": project.bao_city,
                    "czry": project.czry,
                },
            }

            daily_stats[file_date]["files"].append(file_info)
            daily_stats[file_date]["total_files"] += 1

        # 转换为列表并按日期排序
        result = list(daily_stats.values())
        result.sort(key=lambda x: x["date"], reverse=True)

        # 计算总计
        total_files = sum(day["total_files"] for day in result)

        # 构建权限信息
        permission_info = {
            "has_key": access_key is not None,
            "allowed_cities": allowed_cities if allowed_cities else Sql_City,
            "is_admin": access_key == "admin2024" if access_key else False
        }

        logger.info(f"文件统计查询成功 - 总文件数: {total_files} - 权限城市: {len(allowed_cities) if allowed_cities else len(Sql_City)} - IP: {client_ip}")

        return (
            jsonify(
                {
                    "success": True,
                    "data": {
                        "summary": {
                            "total_files": total_files,
                            "total_days": len(result),
                            "start_date": time.strftime(
                                "%Y-%m-%d", time.localtime(start_timestamp)
                            ),
                            "end_date": time.strftime(
                                "%Y-%m-%d", time.localtime(end_timestamp)
                            ),
                        },
                        "daily_stats": result,
                        "permission": permission_info,
                    },
                }
            ),
            200,
        )

    except Exception as e:
        logger.error(f"文件统计查询失败 - IP: {client_ip} - 错误: {str(e)}")
        return jsonify({"success": False, "msg": f"查询失败: {str(e)}"}), 500


if __name__ == "__main__":
    logger.info("开始启动服务器")
    logger.info("服务器配置 - 端口: 5000 - 线程数: 20")
    serve(
        app,
        host="0.0.0.0",
        port=5000,
        threads=20,  # 增加线程数以处理更多并发请求
        connection_limit=1000,  # 连接限制
        cleanup_interval=30,  # 清理间隔
        channel_timeout=120,  # 通道超时
        max_request_body_size=104857600,  # 100MB请求体大小限制
        # send_bytes=18000,  # 发送字节数
        asyncore_use_poll=True,  # 使用poll而不是select
        expose_tracebacks=False,  # 生产环境不暴露traceback
        ident="huayu",  # 服务器标识
    )

